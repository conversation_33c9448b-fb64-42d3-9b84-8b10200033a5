[{"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_4", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "subject", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2020-04-17 18:10:34.082349", "name": "Issue-column_break_4", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "property_name", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 1, "insert_after": "column_break_4", "label": "Property Name", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:30.067591", "name": "Issue-property_name", "no_copy": 0, "non_negative": 0, "options": "Property", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 1, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_15", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:18.343718", "name": "Issue-section_break_15", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue Materials Detail", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "mateiral_request", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "material_status", "label": "Mateiral Request", "length": 0, "mandatory_depends_on": null, "modified": "2021-01-08 10:37:59.202031", "name": "Issue Materials Detail-mateiral_request", "no_copy": 0, "non_negative": 0, "options": "Material Request", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "person_in_charge", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 1, "insert_after": "section_break_15", "label": "Person in charge", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:35.683043", "name": "Issue-person_in_charge", "no_copy": 0, "non_negative": 0, "options": "Employee", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 1, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "sub_contractor_contact", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "person_in_charge", "label": "Sub Contractor", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:47.462346", "name": "Issue-sub_contractor_contact", "no_copy": 0, "non_negative": 0, "options": "Supplier", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "col_brk_001", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "sub_contractor_contact", "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:24.374829", "name": "Issue-col_brk_001", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": "person_in_charge.employee_name", "fetch_if_empty": 0, "fieldname": "person_in_charge_name", "fieldtype": "Read Only", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "col_brk_001", "label": "Person In Charge Name", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:28:44.136810", "name": "Issue-person_in_charge_name", "no_copy": 0, "non_negative": 0, "options": "", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": "sub_contractor_contact.supplier_name", "fetch_if_empty": 0, "fieldname": "sub_contractor_name", "fieldtype": "Read Only", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "person_in_charge_name", "label": "Sub Contractor Name", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:05.121272", "name": "Issue-sub_contractor_name", "no_copy": 0, "non_negative": 0, "options": "", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Quotation", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "cost_center", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "order_type", "label": "Cost Center", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-20 19:21:28.874674", "name": "Quotation-cost_center", "no_copy": 0, "non_negative": 0, "options": "Cost Center", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_14", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "description", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:28:03.654075", "name": "Issue-column_break_14", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "defect_found", "fieldtype": "Text Editor", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_14", "label": "Defect Found", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:28:09.663327", "name": "Issue-defect_found", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "material_request", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "defect_found", "label": "Material Request", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:58.580301", "name": "Issue-material_request", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "materials_required", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "material_request", "label": "Materials Required", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:53.694881", "name": "Issue-materials_required", "no_copy": 0, "non_negative": 0, "options": "Issue Materials Detail", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "materials_billed", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "materials_required", "label": "Materials Billed", "length": 0, "mandatory_depends_on": null, "modified": "2020-04-17 18:10:50.450539", "name": "Issue-materials_billed", "no_copy": 0, "non_negative": 0, "options": "Issue Materials Billed", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Material Request", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "sales_invoice", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "job_card", "label": "Sales Invoice", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:41.928992", "name": "Material Request-sales_invoice", "no_copy": 0, "non_negative": 0, "options": "Sales Invoice", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:!doc.__islocal", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Issue", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "customer_feedback", "fieldtype": "Text Editor", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break1", "label": "Customer <PERSON><PERSON><PERSON>", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:27:12.072483", "name": "Issue-customer_feedback", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Material Request Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "material_request", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "page_break", "label": "Material Request", "length": 0, "mandatory_depends_on": null, "modified": "2021-01-08 10:39:21.402224", "name": "Material Request Item-material_request", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": "If the item requires Gas or Electricity or similar meter reading to charge the customer.", "docstatus": 0, "doctype": "Custom Field", "dt": "<PERSON><PERSON>", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reading_required", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_sales_item", "label": "Reading required", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:24:45.938599", "name": "Item-reading_required", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "property_management_settings", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "expenses_included_in_valuation", "label": "Property Management Settings", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 05:20:19.926416", "name": "Company-property_management_settings", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "security_account_code", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "property_management_settings", "label": "Security Account Code", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 05:20:20.640276", "name": "Company-security_account_code", "no_copy": 0, "non_negative": 0, "options": "Account", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "default_tax_account_head", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "security_account_code", "label": "Default Tax Account Head", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 05:20:21.521117", "name": "Company-default_tax_account_head", "no_copy": 0, "non_negative": 0, "options": "Account", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "default_tax_template", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "default_tax_account_head", "label": "Default Tax Template", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 05:20:22.287077", "name": "Company-default_tax_template", "no_copy": 0, "non_negative": 0, "options": "Sales Taxes and Charges Template", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "default_maintenance_tax_template", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "default_tax_template", "label": "Default Maintenance Tax Template", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 05:20:23.092180", "name": "Company-default_maintenance_tax_template", "no_copy": 0, "non_negative": 0, "options": "Sales Taxes and Charges Template", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval: !in_list(frappe.user_roles, \"Healthcare Receptionist\")", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "lease_information", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "select_print_heading", "label": "Lease Information", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:25:40.535861", "name": "Sales Invoice-lease_information", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "lease", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "lease_information", "label": "Lease", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:25:56.046693", "name": "Sales Invoice-lease", "no_copy": 0, "non_negative": 0, "options": "Lease", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "lease_item", "fieldtype": "Read Only", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "lease", "label": "Lease Item", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-05 15:25:24.126654", "name": "Sales Invoice-lease_item", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "job_card", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "lease_item", "label": "Job Card", "length": 0, "mandatory_depends_on": null, "modified": "2020-04-16 17:05:14.944120", "name": "Sales Invoice-job_card", "no_copy": 0, "non_negative": 0, "options": "Issue", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}]