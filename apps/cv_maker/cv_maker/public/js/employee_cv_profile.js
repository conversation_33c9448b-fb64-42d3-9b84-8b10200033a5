// Copyright (c) 2025, Your Organization and contributors
// For license information, please see license.txt

// Utility function for copying text to clipboard
function copyToClipboard(text) {
    // Create a temporary input element
    const tempInput = document.createElement('input');
    tempInput.style.position = 'absolute';
    tempInput.style.left = '-1000px';
    tempInput.value = text;
    document.body.appendChild(tempInput);

    // Select and copy the text
    tempInput.select();
    document.execCommand('copy');

    // Clean up
    document.body.removeChild(tempInput);

    return true;
}

frappe.ui.form.on('Employee CV Profile', {
	refresh: function(frm) {
		if (!frm.is_new()) {
			// Add export buttons
			frm.add_custom_button(__('Export as PDF'), function() {
				export_cv(frm, 'PDF');
			}, __('Export'));

			frm.add_custom_button(__('Export as DOCX'), function() {
				export_cv(frm, 'DOCX');
			}, __('Export'));

			frm.add_custom_button(__('Export as HTML'), function() {
				export_cv(frm, 'HTML');
			}, __('Export'));

			// Add sharing button
			frm.add_custom_button(__('Generate Shareable Link'), function() {
				generate_public_link(frm);
			}, __('Share'));

			// Add completeness score indicator
			show_completeness_score(frm);
		}
	},

	onload: function(frm) {
		// Set up field filters
		frm.set_query('employee', function() {
			return {
				filters: {
					status: 'Active'
				}
			};
		});
	},

	employee: function(frm) {
		if (frm.doc.employee) {
			// Auto-fetch employee name and set CV title
			frappe.call({
				method: 'frappe.client.get_value',
				args: {
					doctype: 'Employee',
					filters: {name: frm.doc.employee},
					fieldname: ['employee_name', 'designation']
				},
				callback: function(r) {
					if (r.message) {
						frm.set_value('employee_name', r.message.employee_name);
						if (!frm.doc.cv_title && r.message.designation) {
							frm.set_value('cv_title', `${r.message.designation} - ${r.message.employee_name}`);
						}
					}
				}
			});
		}
	}
});

function export_cv(frm, format) {
	frappe.call({
		method: 'cv_maker.api.export_cv_profile',
		args: {
			cv_profile_name: frm.doc.name,
			export_format: format
		},
		callback: function(r) {
			if (r.message) {
				if (format === 'HTML') {
					// Open HTML in new window
					let html_window = window.open('', '_blank');
					html_window.document.write(r.message);
					html_window.document.close();
				} else {
					// Handle binary data for PDF and DOCX
					let binary_data;
					if (typeof r.message === 'string') {
						// Convert base64 string to binary
						binary_data = atob(r.message);
					} else {
						binary_data = r.message;
					}

					// Convert to Uint8Array for proper binary handling
					let bytes = new Uint8Array(binary_data.length);
					for (let i = 0; i < binary_data.length; i++) {
						bytes[i] = binary_data.charCodeAt(i);
					}

					// Create blob with proper MIME type
					let blob = new Blob([bytes], {
						type: format === 'PDF' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
					});

					// Download file
					let url = window.URL.createObjectURL(blob);
					let a = document.createElement('a');
					a.href = url;
					a.download = `${frm.doc.employee_name}_CV.${format.toLowerCase()}`;
					document.body.appendChild(a);
					a.click();
					document.body.removeChild(a);
					window.URL.revokeObjectURL(url);
				}

				frappe.show_alert({
					message: __(`CV exported as ${format} successfully`),
					indicator: 'green'
				});
			}
		},
		error: function(r) {
			frappe.show_alert({
				message: __(`Error exporting CV: ${r.message || 'Unknown error'}`),
				indicator: 'red'
			});
		}
	});
}

function generate_public_link(frm) {
	let d = new frappe.ui.Dialog({
		title: __('Generate Shareable Link'),
		fields: [
			{
				fieldtype: 'Int',
				fieldname: 'valid_days',
				label: __('Valid for (days)'),
				default: 30,
				reqd: 1
			}
		],
		primary_action_label: __('Generate Link'),
		primary_action: function() {
			let values = d.get_values();
			frappe.call({
				method: 'cv_maker.api.generate_public_link',
				args: {
					cv_profile_name: frm.doc.name,
					valid_days: values.valid_days
				},
				callback: function(r) {
					if (r.message) {
						d.hide();

						// Show the generated links
						frappe.msgprint({
							title: __('Shareable Links Generated'),
							message: `
								<div class="form-group">
									<label>${__('Direct Link')}:</label>
									<div class="input-group mb-3">
										<input type="text" class="form-control direct-link-input" value="${r.message.direct_view_url}" readonly>
										<div class="input-group-append">
											<button class="btn btn-outline-secondary copy-btn" type="button" data-clipboard-text="${r.message.direct_view_url}">
												${__('Copy')}
											</button>
										</div>
									</div>

									<label>${__('App Link')}:</label>
									<div class="input-group">
										<input type="text" class="form-control app-link-input" value="${r.message.public_url}" readonly>
										<div class="input-group-append">
											<button class="btn btn-outline-secondary copy-btn" type="button" data-clipboard-text="${r.message.public_url}">
												${__('Copy')}
											</button>
										</div>
									</div>
								</div>
								<p><small class="text-muted">${__('Valid until')}: ${r.message.valid_until}</small></p>
								<script>
									// Initialize clipboard functionality
									$('.copy-btn').click(function() {
										var text = $(this).data('clipboard-text');
										var button = $(this);

										try {
											// Try modern clipboard API first
											if (navigator.clipboard && window.isSecureContext) {
												navigator.clipboard.writeText(text).then(function() {
													button.html('Copied!');
													setTimeout(() => {
														button.html('Copy');
													}, 2000);
												}).catch(function() {
													// Fallback to execCommand
													if (copyToClipboard(text)) {
														button.html('Copied!');
														setTimeout(() => {
															button.html('Copy');
														}, 2000);
													}
												});
											} else {
												// Fallback to execCommand
												if (copyToClipboard(text)) {
													button.html('Copied!');
													setTimeout(() => {
														button.html('Copy');
													}, 2000);
												}
											}
										} catch (err) {
											console.error('Failed to copy text: ', err);
											frappe.show_alert({
												message: __('Failed to copy to clipboard'),
												indicator: 'red'
											});
										}
									});
								</script>
							`,
							primary_action: {
								label: __('Open Direct Link'),
								action: function() {
									window.open(r.message.direct_view_url, '_blank');
								}
							},
							secondary_action: {
								label: __('Open App Link'),
								action: function() {
									window.open(r.message.public_url, '_blank');
								}
							}
						});

						// Refresh form to show updated token info
						frm.reload_doc();
					}
				}
			});
		}
	});
	d.show();
}

function show_completeness_score(frm) {
	frappe.call({
		method: 'cv_maker.api.get_cv_completeness_score',
		args: {cv_profile_name: frm.doc.name},
		callback: function(r) {
			if (r.message !== undefined) {
				let score = r.message;
				let color = score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red';
				
				// Add completeness indicator to form
				frm.dashboard.add_indicator(__('CV Completeness: {0}%', [score]), color);
				
				// Add suggestions if score is low
				if (score < 80) {
					let suggestions = [];
					if (!frm.doc.summary) suggestions.push('Add Professional Summary');
					if (!frm.doc.skills || frm.doc.skills.length === 0) suggestions.push('Add Skills');
					if (!frm.doc.projects || frm.doc.projects.length === 0) suggestions.push('Add Projects');
					if (!frm.doc.certifications || frm.doc.certifications.length === 0) suggestions.push('Add Certifications');
					if (!frm.doc.languages || frm.doc.languages.length === 0) suggestions.push('Add Languages');
					
					if (suggestions.length > 0) {
						frm.dashboard.add_comment(__('Suggestions to improve CV: {0}', [suggestions.join(', ')]), 'blue', true);
					}
				}
			}
		}
	});
}
