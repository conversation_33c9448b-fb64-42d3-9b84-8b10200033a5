[{"doctype": "CV Template", "name": "Minimalist CV", "template_name": "Minimalist CV", "template_type": "Minimalist", "description": "Clean and simple CV template with minimal design elements", "is_default": 1, "status": "Active", "template_html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ employee_name }} - CV</title>\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: 'Arial', sans-serif;\n            line-height: 1.6;\n            color: #333;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            background: white;\n        }\n        \n        .header {\n            text-align: center;\n            border-bottom: 2px solid #333;\n            padding-bottom: 20px;\n            margin-bottom: 30px;\n        }\n        \n        .header h1 {\n            font-size: 2.5em;\n            font-weight: 300;\n            margin-bottom: 5px;\n        }\n        \n        .header h2 {\n            font-size: 1.2em;\n            font-weight: 400;\n            color: #666;\n            margin-bottom: 10px;\n        }\n        \n        .contact-info {\n            font-size: 0.9em;\n            color: #666;\n        }\n        \n        .section {\n            margin-bottom: 30px;\n        }\n        \n        .section-title {\n            font-size: 1.3em;\n            font-weight: 600;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n            border-bottom: 1px solid #ddd;\n            padding-bottom: 5px;\n            margin-bottom: 15px;\n        }\n        \n        .summary {\n            font-size: 1em;\n            line-height: 1.7;\n            text-align: justify;\n        }\n        \n        .skills-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 10px;\n        }\n        \n        .skill-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 8px 0;\n            border-bottom: 1px solid #eee;\n        }\n        \n        .skill-name {\n            font-weight: 500;\n        }\n        \n        .skill-level {\n            font-size: 0.8em;\n            color: #666;\n            background: #f5f5f5;\n            padding: 2px 8px;\n            border-radius: 3px;\n        }\n        \n        .project-item, .cert-item, .lang-item {\n            margin-bottom: 20px;\n            padding-bottom: 15px;\n            border-bottom: 1px solid #eee;\n        }\n        \n        .project-title, .cert-name {\n            font-weight: 600;\n            font-size: 1.1em;\n            margin-bottom: 5px;\n        }\n        \n        .project-meta, .cert-meta {\n            font-size: 0.9em;\n            color: #666;\n            margin-bottom: 8px;\n        }\n        \n        .project-description {\n            line-height: 1.6;\n        }\n        \n        .languages-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 10px;\n        }\n        \n        .lang-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid #eee;\n            padding-bottom: 8px;\n            margin-bottom: 8px;\n        }\n        \n        @media print {\n            body {\n                padding: 0;\n                max-width: none;\n            }\n            .section {\n                page-break-inside: avoid;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>{{ employee_name }}</h1>\n        <h2>{{ cv_title or designation }}</h2>\n        <div class=\"contact-info\">\n            {% if personal_email %}{{ personal_email }}{% endif %}\n            {% if cell_number %} | {{ cell_number }}{% endif %}\n            {% if company %} | {{ company }}{% endif %}\n        </div>\n    </div>\n\n    {% if summary %}\n    <div class=\"section\">\n        <div class=\"section-title\">Professional Summary</div>\n        <div class=\"summary\">{{ summary }}</div>\n    </div>\n    {% endif %}\n\n    {% if skills %}\n    <div class=\"section\">\n        <div class=\"section-title\">Skills</div>\n        <div class=\"skills-grid\">\n            {% for skill in skills %}\n            <div class=\"skill-item\">\n                <span class=\"skill-name\">{{ skill.skill_name }}</span>\n                <span class=\"skill-level\">{{ skill.proficiency_level }}</span>\n            </div>\n            {% endfor %}\n        </div>\n    </div>\n    {% endif %}\n\n    {% if projects %}\n    <div class=\"section\">\n        <div class=\"section-title\">Projects</div>\n        {% for project in projects %}\n        <div class=\"project-item\">\n            <div class=\"project-title\">{{ project.project_title }}</div>\n            <div class=\"project-meta\">\n                {% if project.client %}Client: {{ project.client }}{% endif %}\n                {% if project.role %} | Role: {{ project.role }}{% endif %}\n                {% if project.duration_from %} | {{ project.duration_from }}{% endif %}\n                {% if project.duration_to %} - {{ project.duration_to }}{% endif %}\n            </div>\n            {% if project.description %}\n            <div class=\"project-description\">{{ project.description }}</div>\n            {% endif %}\n            {% if project.technologies %}\n            <div class=\"project-meta\"><strong>Technologies:</strong> {{ project.technologies }}</div>\n            {% endif %}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n\n    {% if certifications %}\n    <div class=\"section\">\n        <div class=\"section-title\">Certifications</div>\n        {% for cert in certifications %}\n        <div class=\"cert-item\">\n            <div class=\"cert-name\">{{ cert.certification_name }}</div>\n            <div class=\"cert-meta\">\n                {% if cert.issuing_organization %}{{ cert.issuing_organization }}{% endif %}\n                {% if cert.date_awarded %} | Awarded: {{ cert.date_awarded }}{% endif %}\n                {% if cert.expiry_date %} | Expires: {{ cert.expiry_date }}{% endif %}\n            </div>\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n\n    {% if languages %}\n    <div class=\"section\">\n        <div class=\"section-title\">Languages</div>\n        <div class=\"languages-grid\">\n            {% for lang in languages %}\n            <div class=\"lang-item\">\n                <span class=\"skill-name\">{{ lang.language }}</span>\n                <span class=\"skill-level\">{{ lang.proficiency }}</span>\n            </div>\n            {% endfor %}\n        </div>\n    </div>\n    {% endif %}\n</body>\n</html>", "custom_css": ""}, {"doctype": "CV Template", "name": "Corporate CV", "template_name": "Corporate CV", "template_type": "Corporate", "description": "Professional corporate-style CV template with formal design", "is_default": 0, "status": "Active", "template_html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ employee_name }} - CV</title>\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: 'Times New Roman', serif;\n            line-height: 1.5;\n            color: #2c3e50;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 30px;\n            background: white;\n        }\n        \n        .header {\n            background: #34495e;\n            color: white;\n            padding: 30px;\n            text-align: center;\n            margin: -30px -30px 30px -30px;\n        }\n        \n        .header h1 {\n            font-size: 2.8em;\n            font-weight: bold;\n            margin-bottom: 10px;\n        }\n        \n        .header h2 {\n            font-size: 1.4em;\n            font-weight: 300;\n            margin-bottom: 15px;\n        }\n        \n        .contact-info {\n            font-size: 1em;\n            opacity: 0.9;\n        }\n        \n        .section {\n            margin-bottom: 35px;\n        }\n        \n        .section-title {\n            font-size: 1.4em;\n            font-weight: bold;\n            color: #34495e;\n            text-transform: uppercase;\n            letter-spacing: 2px;\n            border-bottom: 3px solid #3498db;\n            padding-bottom: 8px;\n            margin-bottom: 20px;\n        }\n        \n        .summary {\n            font-size: 1.1em;\n            line-height: 1.8;\n            text-align: justify;\n            font-style: italic;\n        }\n        \n        .skills-list {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 15px;\n        }\n        \n        .skill-item {\n            background: #ecf0f1;\n            padding: 12px 15px;\n            border-left: 4px solid #3498db;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .skill-name {\n            font-weight: bold;\n            color: #2c3e50;\n        }\n        \n        .skill-level {\n            background: #3498db;\n            color: white;\n            padding: 4px 10px;\n            border-radius: 15px;\n            font-size: 0.85em;\n            font-weight: bold;\n        }\n        \n        .project-item, .cert-item {\n            background: #f8f9fa;\n            padding: 20px;\n            margin-bottom: 15px;\n            border-left: 5px solid #3498db;\n        }\n        \n        .project-title, .cert-name {\n            font-weight: bold;\n            font-size: 1.2em;\n            color: #2c3e50;\n            margin-bottom: 8px;\n        }\n        \n        .project-meta, .cert-meta {\n            font-size: 0.95em;\n            color: #7f8c8d;\n            margin-bottom: 10px;\n            font-weight: 500;\n        }\n        \n        .project-description {\n            line-height: 1.7;\n            color: #34495e;\n        }\n        \n        .languages-list {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 10px;\n        }\n        \n        .lang-item {\n            background: #e8f4fd;\n            padding: 10px 15px;\n            border-radius: 5px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .lang-name {\n            font-weight: bold;\n            color: #2c3e50;\n        }\n        \n        .lang-level {\n            color: #3498db;\n            font-weight: 500;\n        }\n        \n        @media print {\n            body {\n                padding: 0;\n                max-width: none;\n            }\n            .header {\n                margin: 0 0 30px 0;\n            }\n            .section {\n                page-break-inside: avoid;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>{{ employee_name }}</h1>\n        <h2>{{ cv_title or designation }}</h2>\n        <div class=\"contact-info\">\n            {% if personal_email %}{{ personal_email }}{% endif %}\n            {% if cell_number %} | {{ cell_number }}{% endif %}\n            {% if company %} | {{ company }}{% endif %}\n        </div>\n    </div>\n\n    {% if summary %}\n    <div class=\"section\">\n        <div class=\"section-title\">Executive Summary</div>\n        <div class=\"summary\">{{ summary }}</div>\n    </div>\n    {% endif %}\n\n    {% if skills %}\n    <div class=\"section\">\n        <div class=\"section-title\">Core Competencies</div>\n        <div class=\"skills-list\">\n            {% for skill in skills %}\n            <div class=\"skill-item\">\n                <span class=\"skill-name\">{{ skill.skill_name }}</span>\n                <span class=\"skill-level\">{{ skill.proficiency_level }}</span>\n            </div>\n            {% endfor %}\n        </div>\n    </div>\n    {% endif %}\n\n    {% if projects %}\n    <div class=\"section\">\n        <div class=\"section-title\">Professional Experience</div>\n        {% for project in projects %}\n        <div class=\"project-item\">\n            <div class=\"project-title\">{{ project.project_title }}</div>\n            <div class=\"project-meta\">\n                {% if project.client %}{{ project.client }}{% endif %}\n                {% if project.role %} | {{ project.role }}{% endif %}\n                {% if project.duration_from %} | {{ project.duration_from }}{% endif %}\n                {% if project.duration_to %} - {{ project.duration_to }}{% endif %}\n            </div>\n            {% if project.description %}\n            <div class=\"project-description\">{{ project.description }}</div>\n            {% endif %}\n            {% if project.technologies %}\n            <div class=\"project-meta\"><strong>Technologies:</strong> {{ project.technologies }}</div>\n            {% endif %}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n\n    {% if certifications %}\n    <div class=\"section\">\n        <div class=\"section-title\">Professional Certifications</div>\n        {% for cert in certifications %}\n        <div class=\"cert-item\">\n            <div class=\"cert-name\">{{ cert.certification_name }}</div>\n            <div class=\"cert-meta\">\n                {% if cert.issuing_organization %}{{ cert.issuing_organization }}{% endif %}\n                {% if cert.date_awarded %} | Awarded: {{ cert.date_awarded }}{% endif %}\n                {% if cert.expiry_date %} | Expires: {{ cert.expiry_date }}{% endif %}\n            </div>\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n\n    {% if languages %}\n    <div class=\"section\">\n        <div class=\"section-title\">Language Proficiency</div>\n        <div class=\"languages-list\">\n            {% for lang in languages %}\n            <div class=\"lang-item\">\n                <span class=\"lang-name\">{{ lang.language }}</span>\n                <span class=\"lang-level\">{{ lang.proficiency }}</span>\n            </div>\n            {% endfor %}\n        </div>\n    </div>\n    {% endif %}\n</body>\n</html>", "custom_css": ""}, {"doctype": "CV Template", "name": "Creative CV", "template_name": "Creative CV", "template_type": "Creative", "description": "Modern and creative CV template with colorful design elements", "is_default": 0, "status": "Active", "template_html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ employee_name }} - CV</title>\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n            padding: 20px;\n        }\n        \n        .cv-container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 15px;\n            overflow: hidden;\n            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n        }\n        \n        .header {\n            background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n            color: white;\n            padding: 40px 30px;\n            text-align: center;\n            position: relative;\n        }\n        \n        .header::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"70\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"90\" cy=\"30\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></svg>');\n        }\n        \n        .header h1 {\n            font-size: 3em;\n            font-weight: 300;\n            margin-bottom: 10px;\n            position: relative;\n            z-index: 1;\n        }\n        \n        .header h2 {\n            font-size: 1.3em;\n            font-weight: 400;\n            margin-bottom: 15px;\n            opacity: 0.9;\n            position: relative;\n            z-index: 1;\n        }\n        \n        .contact-info {\n            font-size: 1em;\n            opacity: 0.8;\n            position: relative;\n            z-index: 1;\n        }\n        \n        .content {\n            padding: 30px;\n        }\n        \n        .section {\n            margin-bottom: 35px;\n        }\n        \n        .section-title {\n            font-size: 1.4em;\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 20px;\n            position: relative;\n            padding-left: 20px;\n        }\n        \n        .section-title::before {\n            content: '';\n            position: absolute;\n            left: 0;\n            top: 50%;\n            transform: translateY(-50%);\n            width: 4px;\n            height: 30px;\n            background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n            border-radius: 2px;\n        }\n        \n        .summary {\n            font-size: 1.1em;\n            line-height: 1.8;\n            text-align: justify;\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 10px;\n            border-left: 4px solid #ff6b6b;\n        }\n        \n        .skills-container {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 15px;\n        }\n        \n        .skill-item {\n            background: linear-gradient(135deg, #667eea, #764ba2);\n            color: white;\n            padding: 15px;\n            border-radius: 10px;\n            text-align: center;\n            transform: translateY(0);\n            transition: transform 0.3s ease;\n        }\n        \n        .skill-item:hover {\n            transform: translateY(-5px);\n        }\n        \n        .skill-name {\n            font-weight: 600;\n            font-size: 1.1em;\n            margin-bottom: 5px;\n        }\n        \n        .skill-level {\n            font-size: 0.9em;\n            opacity: 0.9;\n        }\n        \n        .project-item, .cert-item {\n            background: white;\n            border: 1px solid #e9ecef;\n            border-radius: 10px;\n            padding: 25px;\n            margin-bottom: 20px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n            transition: box-shadow 0.3s ease;\n        }\n        \n        .project-item:hover, .cert-item:hover {\n            box-shadow: 0 10px 25px rgba(0,0,0,0.15);\n        }\n        \n        .project-title, .cert-name {\n            font-weight: 600;\n            font-size: 1.3em;\n            color: #2c3e50;\n            margin-bottom: 10px;\n        }\n        \n        .project-meta, .cert-meta {\n            font-size: 0.95em;\n            color: #6c757d;\n            margin-bottom: 12px;\n            font-weight: 500;\n        }\n        \n        .project-description {\n            line-height: 1.7;\n            color: #495057;\n        }\n        \n        .languages-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n            gap: 15px;\n        }\n        \n        .lang-item {\n            background: linear-gradient(135deg, #a8edea, #fed6e3);\n            padding: 15px;\n            border-radius: 10px;\n            text-align: center;\n            color: #2c3e50;\n        }\n        \n        .lang-name {\n            font-weight: 600;\n            font-size: 1.1em;\n            margin-bottom: 5px;\n        }\n        \n        .lang-level {\n            font-size: 0.9em;\n            color: #6c757d;\n        }\n        \n        @media print {\n            body {\n                background: white;\n                padding: 0;\n            }\n            .cv-container {\n                box-shadow: none;\n                border-radius: 0;\n            }\n            .skill-item:hover, .project-item:hover, .cert-item:hover {\n                transform: none;\n                box-shadow: none;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"cv-container\">\n        <div class=\"header\">\n            <h1>{{ employee_name }}</h1>\n            <h2>{{ cv_title or designation }}</h2>\n            <div class=\"contact-info\">\n                {% if personal_email %}{{ personal_email }}{% endif %}\n                {% if cell_number %} | {{ cell_number }}{% endif %}\n                {% if company %} | {{ company }}{% endif %}\n            </div>\n        </div>\n        \n        <div class=\"content\">\n            {% if summary %}\n            <div class=\"section\">\n                <div class=\"section-title\">About Me</div>\n                <div class=\"summary\">{{ summary }}</div>\n            </div>\n            {% endif %}\n\n            {% if skills %}\n            <div class=\"section\">\n                <div class=\"section-title\">Skills & Expertise</div>\n                <div class=\"skills-container\">\n                    {% for skill in skills %}\n                    <div class=\"skill-item\">\n                        <div class=\"skill-name\">{{ skill.skill_name }}</div>\n                        <div class=\"skill-level\">{{ skill.proficiency_level }}</div>\n                    </div>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endif %}\n\n            {% if projects %}\n            <div class=\"section\">\n                <div class=\"section-title\">Featured Projects</div>\n                {% for project in projects %}\n                <div class=\"project-item\">\n                    <div class=\"project-title\">{{ project.project_title }}</div>\n                    <div class=\"project-meta\">\n                        {% if project.client %}{{ project.client }}{% endif %}\n                        {% if project.role %} | {{ project.role }}{% endif %}\n                        {% if project.duration_from %} | {{ project.duration_from }}{% endif %}\n                        {% if project.duration_to %} - {{ project.duration_to }}{% endif %}\n                    </div>\n                    {% if project.description %}\n                    <div class=\"project-description\">{{ project.description }}</div>\n                    {% endif %}\n                    {% if project.technologies %}\n                    <div class=\"project-meta\"><strong>Tech Stack:</strong> {{ project.technologies }}</div>\n                    {% endif %}\n                </div>\n                {% endfor %}\n            </div>\n            {% endif %}\n\n            {% if certifications %}\n            <div class=\"section\">\n                <div class=\"section-title\">Certifications & Awards</div>\n                {% for cert in certifications %}\n                <div class=\"cert-item\">\n                    <div class=\"cert-name\">{{ cert.certification_name }}</div>\n                    <div class=\"cert-meta\">\n                        {% if cert.issuing_organization %}{{ cert.issuing_organization }}{% endif %}\n                        {% if cert.date_awarded %} | Awarded: {{ cert.date_awarded }}{% endif %}\n                        {% if cert.expiry_date %} | Expires: {{ cert.expiry_date }}{% endif %}\n                    </div>\n                </div>\n                {% endfor %}\n            </div>\n            {% endif %}\n\n            {% if languages %}\n            <div class=\"section\">\n                <div class=\"section-title\">Languages</div>\n                <div class=\"languages-grid\">\n                    {% for lang in languages %}\n                    <div class=\"lang-item\">\n                        <div class=\"lang-name\">{{ lang.language }}</div>\n                        <div class=\"lang-level\">{{ lang.proficiency }}</div>\n                    </div>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endif %}\n        </div>\n    </div>\n</body>\n</html>", "custom_css": ""}]