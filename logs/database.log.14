2025-07-14 16:18:58,468 WARNING database DDL Query made to DB:
create table `tabLoyalty Program Collection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tier_name` varchar(140),
`min_spent` decimal(21,9) not null default 0,
`collection_factor` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:58,600 WARNING database DDL Query made to DB:
create table `tabTax Withholding Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`tax_withholding_rate` decimal(21,9) not null default 0,
`single_threshold` decimal(21,9) not null default 0,
`cumulative_threshold` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:58,727 WARNING database DDL Query made to DB:
create sequence if not exists bisect_nodes_id_seq nocache nocycle
2025-07-14 16:18:58,746 WARNING database DDL Query made to DB:
create table `tabBisect Nodes` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`root` varchar(140),
`left_child` varchar(140),
`right_child` varchar(140),
`period_from_date` datetime(6),
`period_to_date` datetime(6),
`difference` decimal(21,9) not null default 0,
`balance_sheet_summary` decimal(21,9) not null default 0,
`profit_loss_summary` decimal(21,9) not null default 0,
`generated` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:58,992 WARNING database DDL Query made to DB:
create table `tabOpening Invoice Creation Tool Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_number` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`temporary_opening_account` varchar(140),
`posting_date` date,
`due_date` date,
`item_name` varchar(140) default 'Opening Invoice Item',
`outstanding_amount` decimal(21,9) not null default 0,
`qty` varchar(140) default '1',
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:59,296 WARNING database DDL Query made to DB:
create table `tabBank Transaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'ACC-BTN-.YYYY.-',
`date` date,
`status` varchar(140) default 'Pending',
`bank_account` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`deposit` decimal(21,9) not null default 0,
`withdrawal` decimal(21,9) not null default 0,
`currency` varchar(140),
`description` text,
`reference_number` varchar(140),
`transaction_id` varchar(140) unique,
`transaction_type` varchar(50),
`allocated_amount` decimal(21,9) not null default 0,
`unallocated_amount` decimal(21,9) not null default 0,
`party_type` varchar(140),
`party` varchar(140),
`bank_party_name` varchar(140),
`bank_party_account_number` varchar(140),
`bank_party_iban` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:59,484 WARNING database DDL Query made to DB:
create table `tabShipping Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140) unique,
`disabled` int(1) not null default 0,
`shipping_rule_type` varchar(140),
`company` varchar(140),
`account` varchar(140),
`cost_center` varchar(140),
`calculate_based_on` varchar(140) default 'Fixed',
`shipping_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:59,623 WARNING database DDL Query made to DB:
create table `tabPricing Rule Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`pricing_rule` varchar(140),
`item_code` varchar(140),
`margin_type` varchar(140),
`rate_or_discount` varchar(140),
`child_docname` varchar(140),
`rule_applied` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:59,798 WARNING database DDL Query made to DB:
create table `tabAccount` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`account_name` varchar(140),
`account_number` varchar(140),
`is_group` int(1) not null default 0,
`company` varchar(140),
`root_type` varchar(140),
`report_type` varchar(140),
`account_currency` varchar(140),
`parent_account` varchar(140),
`account_type` varchar(140),
`tax_rate` decimal(21,9) not null default 0,
`freeze_account` varchar(140),
`balance_must_be` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`include_in_gross` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent_account`(`parent_account`),
index `account_type`(`account_type`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:18:59,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-07-14 16:18:59,992 WARNING database DDL Query made to DB:
create table `tabPayment Entry Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_name` varchar(140),
`due_date` date,
`bill_no` varchar(140),
`payment_term` varchar(140),
`payment_term_outstanding` decimal(21,9) not null default 0,
`account_type` varchar(140),
`payment_type` varchar(140),
`reconcile_effect_on` date,
`total_amount` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`exchange_rate` decimal(21,9) not null default 0,
`exchange_gain_loss` decimal(21,9) not null default 0,
`account` varchar(140),
`payment_request` varchar(140),
index `reference_doctype`(`reference_doctype`),
index `reference_name`(`reference_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,131 WARNING database DDL Query made to DB:
create table `tabExchange Rate Revaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`rounding_loss_allowance` decimal(21,9) not null default 0.05,
`company` varchar(140),
`gain_loss_unbooked` decimal(21,9) not null default 0,
`gain_loss_booked` decimal(21,9) not null default 0,
`total_gain_loss` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,266 WARNING database DDL Query made to DB:
create table `tabProcess Deferred Accounting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`type` varchar(140),
`account` varchar(140),
`posting_date` date,
`start_date` date,
`end_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,378 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts CC` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cc` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,580 WARNING database DDL Query made to DB:
create table `tabLedger Health Monitor Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,686 WARNING database DDL Query made to DB:
create table `tabBank Account Subtype` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account_subtype` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,822 WARNING database DDL Query made to DB:
create table `tabParty Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`primary_role` varchar(140),
`secondary_role` varchar(140),
`primary_party` varchar(140),
`secondary_party` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:00,996 WARNING database DDL Query made to DB:
create table `tabProcess Payment Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`receivable_payable_account` varchar(140),
`default_advance_account` varchar(140),
`from_invoice_date` date,
`to_invoice_date` date,
`from_payment_date` date,
`to_payment_date` date,
`cost_center` varchar(140),
`bank_cash_account` varchar(140),
`status` varchar(140),
`error_log` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,153 WARNING database DDL Query made to DB:
create table `tabAdvance Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`add_deduct_tax` varchar(140),
`charge_type` varchar(140),
`row_id` varchar(140),
`account_head` varchar(140),
`description` text,
`included_in_paid_amount` int(1) not null default 0,
`cost_center` varchar(140),
`rate` decimal(21,9) not null default 0,
`currency` varchar(140),
`tax_amount` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`base_tax_amount` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
index `account_head`(`account_head`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,256 WARNING database DDL Query made to DB:
create table `tabShipping Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_value` decimal(21,9) not null default 0,
`to_value` decimal(21,9) not null default 0,
`shipping_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,484 WARNING database DDL Query made to DB:
create table `tabPayment Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_request_type` varchar(140) default 'Inward',
`transaction_date` date,
`naming_series` varchar(140),
`company` varchar(140),
`mode_of_payment` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`party_name` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`grand_total` decimal(21,9) not null default 0,
`currency` varchar(140),
`is_a_subscription` int(1) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`party_account_currency` varchar(140),
`bank_account` varchar(140),
`bank` varchar(140),
`bank_account_no` varchar(140),
`account` varchar(140),
`iban` varchar(140),
`branch_code` varchar(140),
`swift_number` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`print_format` varchar(140),
`email_to` varchar(140),
`subject` varchar(140),
`payment_gateway_account` varchar(140),
`status` varchar(140) default 'Draft',
`make_sales_invoice` int(1) not null default 0,
`message` text,
`mute_email` int(1) not null default 0,
`payment_url` varchar(500),
`payment_gateway` varchar(140),
`payment_account` varchar(140),
`payment_channel` varchar(140),
`payment_order` varchar(140),
`amended_from` varchar(140),
`phone_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,604 WARNING database DDL Query made to DB:
create table `tabBank Account Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,752 WARNING database DDL Query made to DB:
create table `tabSales Taxes and Charges Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`is_default` int(1) not null default 0,
`disabled` int(1) not null default 0,
`company` varchar(140),
`tax_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:01,905 WARNING database DDL Query made to DB:
create table `tabFiscal Year` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`year` varchar(140) unique,
`disabled` int(1) not null default 0,
`is_short_year` int(1) not null default 0,
`year_start_date` date,
`year_end_date` date,
`auto_created` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,100 WARNING database DDL Query made to DB:
create table `tabCashier Closing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'POS-CLO-',
`user` varchar(140),
`date` date,
`from_time` time(6),
`time` time(6),
`expense` decimal(21,9) not null default 0,
`custody` decimal(21,9) not null default 0,
`returns` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,431 WARNING database DDL Query made to DB:
create table `tabBudget` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`budget_against` varchar(140) default 'Cost Center',
`company` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`fiscal_year` varchar(140),
`monthly_distribution` varchar(140),
`amended_from` varchar(140),
`applicable_on_material_request` int(1) not null default 0,
`action_if_annual_budget_exceeded_on_mr` varchar(140) default 'Stop',
`action_if_accumulated_monthly_budget_exceeded_on_mr` varchar(140) default 'Warn',
`applicable_on_purchase_order` int(1) not null default 0,
`action_if_annual_budget_exceeded_on_po` varchar(140) default 'Stop',
`action_if_accumulated_monthly_budget_exceeded_on_po` varchar(140) default 'Warn',
`applicable_on_booking_actual_expenses` int(1) not null default 0,
`action_if_annual_budget_exceeded` varchar(140) default 'Stop',
`action_if_accumulated_monthly_budget_exceeded` varchar(140) default 'Warn',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,630 WARNING database DDL Query made to DB:
create table `tabPromotional Scheme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`apply_on` varchar(140) default 'Item Code',
`disable` int(1) not null default 0,
`mixed_conditions` int(1) not null default 0,
`is_cumulative` int(1) not null default 0,
`apply_rule_on_other` varchar(140),
`other_item_code` varchar(140),
`other_item_group` varchar(140),
`other_brand` varchar(140),
`selling` int(1) not null default 0,
`buying` int(1) not null default 0,
`applicable_for` varchar(140),
`valid_from` date,
`valid_upto` date,
`company` varchar(140),
`currency` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,754 WARNING database DDL Query made to DB:
create table `tabSupplier Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,874 WARNING database DDL Query made to DB:
create table `tabAdvance Tax` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`reference_detail` varchar(140),
`account_head` varchar(140),
`allocated_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:02,981 WARNING database DDL Query made to DB:
create table `tabPOS Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,106 WARNING database DDL Query made to DB:
create table `tabUnreconcile Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,230 WARNING database DDL Query made to DB:
create table `tabMonthly Distribution` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`distribution_id` varchar(140) unique,
`fiscal_year` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `fiscal_year`(`fiscal_year`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,342 WARNING database DDL Query made to DB:
create table `tabSouth Africa VAT Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,487 WARNING database DDL Query made to DB:
create table `tabPayment Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_term_name` varchar(140) unique,
`invoice_portion` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`due_date_based_on` varchar(140),
`credit_days` int(11) not null default 0,
`credit_months` int(11) not null default 0,
`discount_type` varchar(140) default 'Percentage',
`discount` decimal(21,9) not null default 0,
`discount_validity_based_on` varchar(140) default 'Day(s) after invoice date',
`discount_validity` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,656 WARNING database DDL Query made to DB:
create table `tabInvoice Discounting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`loan_start_date` date,
`loan_period` int(11) not null default 0,
`loan_end_date` date,
`status` varchar(140),
`company` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`bank_charges` decimal(21,9) not null default 0,
`short_term_loan` varchar(140),
`bank_account` varchar(140),
`bank_charges_account` varchar(140),
`accounts_receivable_credit` varchar(140),
`accounts_receivable_discounted` varchar(140),
`accounts_receivable_unpaid` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:03,803 WARNING database DDL Query made to DB:
create table `tabTax Withholding Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`round_off_tax_amount` int(1) not null default 0,
`consider_party_ledger_amount` int(1) not null default 0,
`tax_on_excess_amount` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:04,006 WARNING database DDL Query made to DB:
create table `tabCheque Print Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`has_print_format` int(1) not null default 0,
`bank_name` varchar(140),
`cheque_size` varchar(140) default 'Regular',
`starting_position_from_top_edge` decimal(21,9) not null default 0,
`cheque_width` decimal(21,9) not null default 20.0,
`cheque_height` decimal(21,9) not null default 9.0,
`scanned_cheque` text,
`is_account_payable` int(1) not null default 1,
`acc_pay_dist_from_top_edge` decimal(21,9) not null default 1.0,
`acc_pay_dist_from_left_edge` decimal(21,9) not null default 9.0,
`message_to_show` varchar(140) default 'Acc. Payee',
`date_dist_from_top_edge` decimal(21,9) not null default 1.0,
`date_dist_from_left_edge` decimal(21,9) not null default 15.0,
`payer_name_from_top_edge` decimal(21,9) not null default 2.0,
`payer_name_from_left_edge` decimal(21,9) not null default 3.0,
`amt_in_words_from_top_edge` decimal(21,9) not null default 3.0,
`amt_in_words_from_left_edge` decimal(21,9) not null default 4.0,
`amt_in_word_width` decimal(21,9) not null default 15.0,
`amt_in_words_line_spacing` decimal(21,9) not null default 0.5,
`amt_in_figures_from_top_edge` decimal(21,9) not null default 3.5,
`amt_in_figures_from_left_edge` decimal(21,9) not null default 16.0,
`acc_no_dist_from_top_edge` decimal(21,9) not null default 5.0,
`acc_no_dist_from_left_edge` decimal(21,9) not null default 4.0,
`signatory_from_top_edge` decimal(21,9) not null default 6.0,
`signatory_from_left_edge` decimal(21,9) not null default 15.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:04,154 WARNING database DDL Query made to DB:
create table `tabSales Invoice Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`remarks` text,
`reference_row` varchar(140),
`advance_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`exchange_gain_loss` decimal(21,9) not null default 0,
`ref_exchange_rate` decimal(21,9) not null default 0,
`difference_posting_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:04,707 WARNING database DDL Query made to DB:
create table `tabSales Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{customer_name}',
`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` text,
`tax_id` varchar(140),
`company` varchar(140),
`company_tax_id` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`due_date` date,
`is_pos` int(1) not null default 0,
`pos_profile` varchar(140),
`is_consolidated` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`update_outstanding_for_self` int(1) not null default 1,
`update_billed_amount_in_sales_order` int(1) not null default 0,
`update_billed_amount_in_delivery_note` int(1) not null default 1,
`is_debit_note` int(1) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`update_stock` int(1) not null default 0,
`set_warehouse` varchar(140),
`set_target_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` text,
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`use_company_roundoff_cost_center` int(1) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` text,
`total_advance` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`disable_rounded_total` int(1) not null default 0,
`apply_discount_on` varchar(15) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`is_cash_or_non_trade_discount` int(1) not null default 0,
`additional_discount_account` varchar(140),
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`total_billing_hours` decimal(21,9) not null default 0,
`total_billing_amount` decimal(21,9) not null default 0,
`cash_bank_account` varchar(140),
`base_paid_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`base_change_amount` decimal(21,9) not null default 0,
`change_amount` decimal(21,9) not null default 0,
`account_for_change_amount` varchar(140),
`allocate_advances_automatically` int(1) not null default 0,
`only_include_allocated_payments` int(1) not null default 0,
`write_off_amount` decimal(21,9) not null default 0,
`base_write_off_amount` decimal(21,9) not null default 0,
`write_off_outstanding_amount_automatically` int(1) not null default 0,
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`redeem_loyalty_points` int(1) not null default 0,
`loyalty_points` int(11) not null default 0,
`loyalty_amount` decimal(21,9) not null default 0,
`loyalty_program` varchar(140),
`loyalty_redemption_account` varchar(140),
`loyalty_redemption_cost_center` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`territory` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` text,
`dispatch_address_name` varchar(140),
`dispatch_address` text,
`company_address` varchar(140),
`company_address_display` text,
`company_contact_person` varchar(140),
`ignore_default_payment_terms_template` int(1) not null default 0,
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`po_no` varchar(140),
`po_date` date,
`debit_to` varchar(140),
`party_account_currency` varchar(140),
`is_opening` varchar(4) default 'No',
`unrealized_profit_loss_account` varchar(140),
`against_income_account` text,
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) not null default 0,
`commission_rate` decimal(21,9) not null default 0,
`total_commission` decimal(21,9) not null default 0,
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(6),
`subscription` varchar(140),
`from_date` date,
`auto_repeat` varchar(140),
`to_date` date,
`status` varchar(30) default 'Draft',
`inter_company_invoice_reference` varchar(140),
`campaign` varchar(140),
`represents_company` varchar(140),
`source` varchar(140),
`customer_group` varchar(140),
`is_internal_customer` int(1) not null default 0,
`is_discounted` int(1) not null default 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `project`(`project`),
index `debit_to`(`debit_to`),
index `inter_company_invoice_reference`(`inter_company_invoice_reference`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:04,872 WARNING database DDL Query made to DB:
create table `tabPOS Opening Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`period_start_date` datetime(6),
`period_end_date` date,
`status` varchar(140) default 'Draft',
`posting_date` date,
`set_posting_date` int(1) not null default 0,
`company` varchar(140),
`pos_profile` varchar(140),
`pos_closing_entry` varchar(140),
`user` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:05,000 WARNING database DDL Query made to DB:
create table `tabBudget Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
`budget_amount` decimal(21,9) not null default 0,
index `account`(`account`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:05,115 WARNING database DDL Query made to DB:
create table `tabPOS Payment Method` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default` int(1) not null default 0,
`allow_in_returns` int(1) not null default 0,
`mode_of_payment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:05,238 WARNING database DDL Query made to DB:
create table `tabUnreconcile Payment Entries` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`allocated_amount` decimal(21,9) not null default 0,
`account_currency` varchar(140),
`unlinked` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:05,494 WARNING database DDL Query made to DB:
create table `tabGL Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`transaction_date` date,
`fiscal_year` varchar(140),
`due_date` date,
`account` varchar(140),
`account_currency` varchar(140),
`against` text,
`party_type` varchar(140),
`party` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_subtype` text,
`transaction_currency` varchar(140),
`against_voucher_type` varchar(140),
`against_voucher` varchar(140),
`voucher_detail_no` varchar(140),
`transaction_exchange_rate` decimal(21,9) not null default 0,
`debit_in_account_currency` decimal(21,9) not null default 0,
`debit` decimal(21,9) not null default 0,
`debit_in_transaction_currency` decimal(21,9) not null default 0,
`credit_in_account_currency` decimal(21,9) not null default 0,
`credit` decimal(21,9) not null default 0,
`credit_in_transaction_currency` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
`finance_book` varchar(140),
`company` varchar(140),
`is_opening` varchar(140),
`is_advance` varchar(140),
`to_rename` int(1) not null default 1,
`is_cancelled` int(1) not null default 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_date`(`posting_date`),
index `account`(`account`),
index `party_type`(`party_type`),
index `party`(`party`),
index `voucher_no`(`voucher_no`),
index `against_voucher`(`against_voucher`),
index `voucher_detail_no`(`voucher_detail_no`),
index `company`(`company`),
index `to_rename`(`to_rename`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:05,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `voucher_type_voucher_no_index`(voucher_type, voucher_no)
2025-07-14 16:19:05,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `posting_date_company_index`(posting_date, company)
2025-07-14 16:19:05,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `party_type_party_index`(party_type, party)
2025-07-14 16:19:05,711 WARNING database DDL Query made to DB:
create table `tabBank Clearance Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_document` varchar(140),
`payment_entry` varchar(140),
`against_account` varchar(140),
`amount` varchar(140),
`posting_date` date,
`cheque_number` varchar(140),
`cheque_date` date,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,193 WARNING database DDL Query made to DB:
create table `tabPurchase Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`tax_id` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`due_date` date,
`is_paid` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`update_outstanding_for_self` int(1) not null default 1,
`update_billed_amount_in_purchase_order` int(1) not null default 0,
`update_billed_amount_in_purchase_receipt` int(1) not null default 1,
`apply_tds` int(1) not null default 0,
`tax_withholding_category` varchar(140),
`amended_from` varchar(140),
`bill_no` varchar(140),
`bill_date` date,
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`use_transaction_date_exchange_rate` int(1) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`update_stock` int(1) not null default 0,
`set_warehouse` varchar(140),
`set_from_warehouse` varchar(140),
`is_subcontracted` int(1) not null default 0,
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_withholding_net_total` decimal(21,9) not null default 0,
`base_tax_withholding_net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) not null default 0,
`base_taxes_and_charges_deducted` decimal(21,9) not null default 0,
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`taxes_and_charges_added` decimal(21,9) not null default 0,
`taxes_and_charges_deducted` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`use_company_roundoff_cost_center` int(1) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`total_advance` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`disable_rounded_total` int(1) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`mode_of_payment` varchar(140),
`base_paid_amount` decimal(21,9) not null default 0,
`clearance_date` date,
`cash_bank_account` varchar(140),
`paid_amount` decimal(21,9) not null default 0,
`allocate_advances_automatically` int(1) not null default 0,
`only_include_allocated_payments` int(1) not null default 0,
`write_off_amount` decimal(21,9) not null default 0,
`base_write_off_amount` decimal(21,9) not null default 0,
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`dispatch_address` varchar(140),
`dispatch_address_display` longtext,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`payment_terms_template` varchar(140),
`ignore_default_payment_terms_template` int(1) not null default 0,
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) default 'Draft',
`per_received` decimal(21,9) not null default 0,
`credit_to` varchar(140),
`party_account_currency` varchar(140),
`is_opening` varchar(140) default 'No',
`against_expense_account` text,
`unrealized_profit_loss_account` varchar(140),
`subscription` varchar(140),
`auto_repeat` varchar(140),
`from_date` date,
`to_date` date,
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`on_hold` int(1) not null default 0,
`release_date` date,
`hold_comment` text,
`is_internal_supplier` int(1) not null default 0,
`represents_company` varchar(140),
`supplier_group` varchar(140),
`inter_company_invoice_reference` varchar(140),
`is_old_subcontracting_flow` int(1) not null default 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `bill_no`(`bill_no`),
index `credit_to`(`credit_to`),
index `release_date`(`release_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,503 WARNING database DDL Query made to DB:
create table `tabShareholder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`folio_no` varchar(140) unique,
`company` varchar(140),
`is_company` int(1) not null default 0,
`contact_list` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,617 WARNING database DDL Query made to DB:
create table `tabRepost Payment Ledger Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`voucher_type` varchar(140),
`voucher_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,731 WARNING database DDL Query made to DB:
create table `tabShipping Rule Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,830 WARNING database DDL Query made to DB:
create table `tabSales Partner Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sales_partner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:06,941 WARNING database DDL Query made to DB:
create table `tabCampaign Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,190 WARNING database DDL Query made to DB:
create table `tabShare Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`share_type` varchar(140),
`from_no` int(11) not null default 0,
`rate` int(11) not null default 0,
`no_of_shares` int(11) not null default 0,
`to_no` int(11) not null default 0,
`amount` int(11) not null default 0,
`is_company` int(1) not null default 0,
`current_state` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,291 WARNING database DDL Query made to DB:
create table `tabCost Center Allocation Percentage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_center` varchar(140),
`percentage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,403 WARNING database DDL Query made to DB:
create table `tabFinance Book` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`finance_book_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,585 WARNING database DDL Query made to DB:
create table `tabExchange Rate Revaluation Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`account_currency` varchar(140),
`balance_in_account_currency` decimal(21,9) not null default 0,
`new_balance_in_account_currency` decimal(21,9) not null default 0,
`current_exchange_rate` decimal(21,9) not null default 0,
`new_exchange_rate` decimal(21,9) not null default 0,
`balance_in_base_currency` decimal(21,9) not null default 0,
`new_balance_in_base_currency` decimal(21,9) not null default 0,
`gain_loss` decimal(21,9) not null default 0,
`zero_balance` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,756 WARNING database DDL Query made to DB:
create table `tabAccounting Dimension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`label` varchar(140) unique,
`fieldname` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `document_type`(`document_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:07,900 WARNING database DDL Query made to DB:
create table `tabAccounting Dimension Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`accounting_dimension` varchar(140),
`disabled` int(1) not null default 0,
`company` varchar(140),
`apply_restriction_on_values` int(1) not null default 1,
`allow_or_restrict` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:08,016 WARNING database DDL Query made to DB:
create table `tabJournal Entry Template Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:08,345 WARNING database DDL Query made to DB:
create table `tabPricing Rule Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
`uom` varchar(140),
index `item_group`(`item_group`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:08,456 WARNING database DDL Query made to DB:
create table `tabMonthly Distribution Percentage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`month` varchar(140),
`percentage_allocation` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:08,691 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140),
`from_date` date,
`posting_date` date,
`company` varchar(140),
`account` varchar(140),
`categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)',
`territory` varchar(140),
`ignore_exchange_rate_revaluation_journals` int(1) not null default 0,
`ignore_cr_dr_notes` int(1) not null default 0,
`to_date` date,
`finance_book` varchar(140),
`currency` varchar(140),
`payment_terms_template` varchar(140),
`sales_partner` varchar(140),
`sales_person` varchar(140),
`show_remarks` int(1) not null default 0,
`based_on_payment_terms` int(1) not null default 0,
`customer_collection` varchar(140),
`collection_name` varchar(140),
`primary_mandatory` int(1) not null default 1,
`show_net_values_in_party_account` int(1) not null default 0,
`orientation` varchar(140),
`include_break` int(1) not null default 1,
`include_ageing` int(1) not null default 0,
`ageing_based_on` varchar(140) default 'Due Date',
`letter_head` varchar(140),
`terms_and_conditions` varchar(140),
`enable_auto_email` int(1) not null default 0,
`sender` varchar(140),
`frequency` varchar(140),
`filter_duration` int(11) not null default 1,
`start_date` date,
`pdf_name` varchar(140),
`subject` varchar(140),
`body` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:10,864 WARNING database DDL Query made to DB:
create table `tabCompetitor Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`competitor` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:10,975 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:11,092 WARNING database DDL Query made to DB:
create table `tabContract Template Fulfilment Terms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`requirement` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:11,340 WARNING database DDL Query made to DB:
create table `tabAppointment Booking Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:11,475 WARNING database DDL Query made to DB:
create table `tabContract Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`contract_terms` longtext,
`requires_fulfilment` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:11,655 WARNING database DDL Query made to DB:
create table `tabOpportunity Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`brand` varchar(140),
`item_group` varchar(140),
`description` longtext,
`image` text,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:11,765 WARNING database DDL Query made to DB:
create table `tabOpportunity Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:12,070 WARNING database DDL Query made to DB:
create table `tabOpportunity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`opportunity_from` varchar(140),
`party_name` varchar(140),
`customer_name` varchar(140),
`status` varchar(140) default 'Open',
`opportunity_type` varchar(140) default 'Sales',
`source` varchar(140),
`opportunity_owner` varchar(140),
`sales_stage` varchar(140) default 'Prospecting',
`expected_closing` date,
`probability` decimal(21,9) not null default 100.0,
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`customer_group` varchar(140),
`industry` varchar(140),
`market_segment` varchar(140),
`website` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`territory` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`opportunity_amount` decimal(21,9) not null default 0,
`base_opportunity_amount` decimal(21,9) not null default 0,
`company` varchar(140),
`campaign` varchar(140),
`transaction_date` date,
`language` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`first_response_time` decimal(21,9),
`order_lost_reason` text,
`contact_person` varchar(140),
`job_title` varchar(140),
`contact_email` varchar(140),
`contact_mobile` varchar(140),
`whatsapp` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`contact_display` text,
`base_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer_group`(`customer_group`),
index `territory`(`territory`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:12,333 WARNING database DDL Query made to DB:
create table `tabContract Fulfilment Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fulfilled` int(1) not null default 0,
`requirement` varchar(140),
`notes` text,
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:12,451 WARNING database DDL Query made to DB:
create table `tabLead Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140) unique,
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:12,583 WARNING database DDL Query made to DB:
create table `tabCompetitor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`competitor_name` varchar(140) unique,
`website` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:12,871 WARNING database DDL Query made to DB:
create table `tabLead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`salutation` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`lead_name` varchar(140),
`job_title` varchar(140),
`gender` varchar(140),
`source` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140) default 'Lead',
`customer` varchar(140),
`type` varchar(140),
`request_type` varchar(140),
`email_id` varchar(140),
`website` varchar(140),
`mobile_no` varchar(140),
`whatsapp_no` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`company_name` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`industry` varchar(140),
`market_segment` varchar(140),
`territory` varchar(140),
`fax` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`qualification_status` varchar(140),
`qualified_by` varchar(140),
`qualified_on` date,
`campaign_name` varchar(140),
`company` varchar(140),
`language` varchar(140),
`image` text,
`title` varchar(140),
`disabled` int(1) not null default 0,
`unsubscribed` int(1) not null default 0,
`blog_subscriber` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lead_name`(`lead_name`),
index `lead_owner`(`lead_owner`),
index `status`(`status`),
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,004 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,138 WARNING database DDL Query made to DB:
create table `tabLost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,263 WARNING database DDL Query made to DB:
create table `tabAvailability Of Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,397 WARNING database DDL Query made to DB:
create sequence if not exists prospect_opportunity_id_seq nocache nocycle
2025-07-14 16:19:13,416 WARNING database DDL Query made to DB:
create table `tabProspect Opportunity` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`opportunity` varchar(140),
`amount` decimal(21,9) not null default 0,
`stage` varchar(140),
`deal_owner` varchar(140),
`probability` decimal(21,9) not null default 0,
`expected_closing` date,
`currency` varchar(140),
`contact_person` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,542 WARNING database DDL Query made to DB:
create table `tabEmail Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_name` varchar(140),
`email_campaign_for` varchar(140) default 'Lead',
`recipient` varchar(140),
`sender` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,712 WARNING database DDL Query made to DB:
create table `tabAppointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_time` datetime(6),
`status` varchar(140),
`customer_name` varchar(140),
`customer_phone_number` varchar(140),
`customer_skype` varchar(140),
`customer_email` varchar(140),
`customer_details` longtext,
`appointment_with` varchar(140),
`party` varchar(140),
`calendar_event` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:13,853 WARNING database DDL Query made to DB:
create table `tabMarket Segment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`market_segment` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,042 WARNING database DDL Query made to DB:
create table `tabContract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`party_type` varchar(140) default 'Customer',
`is_signed` int(1) not null default 0,
`party_name` varchar(140),
`party_user` varchar(140),
`status` varchar(140),
`fulfilment_status` varchar(140),
`party_full_name` varchar(140),
`start_date` date,
`end_date` date,
`signee` varchar(140),
`signed_on` datetime(6),
`ip_address` varchar(140),
`contract_template` varchar(140),
`contract_terms` longtext,
`requires_fulfilment` int(1) not null default 0,
`fulfilment_deadline` date,
`signee_company` longtext,
`signed_by_company` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,186 WARNING database DDL Query made to DB:
create table `tabCampaign Email Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_template` varchar(140),
`send_after_days` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,299 WARNING database DDL Query made to DB:
create sequence if not exists crm_note_id_seq nocache nocycle
2025-07-14 16:19:14,318 WARNING database DDL Query made to DB:
create table `tabCRM Note` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`note` longtext,
`added_by` varchar(140),
`added_on` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,476 WARNING database DDL Query made to DB:
create table `tabCampaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_name` varchar(140),
`naming_series` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,612 WARNING database DDL Query made to DB:
create table `tabSales Stage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`stage_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,804 WARNING database DDL Query made to DB:
create table `tabProspect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`customer_group` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`market_segment` varchar(140),
`industry` varchar(140),
`territory` varchar(140),
`prospect_owner` varchar(140),
`website` varchar(140),
`fax` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:14,935 WARNING database DDL Query made to DB:
create table `tabProspect Lead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lead` varchar(140),
`lead_name` varchar(140),
`email` varchar(140),
`mobile_no` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:15,381 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standing_name` varchar(140) unique,
`standing_color` varchar(140),
`min_grade` decimal(21,9) not null default 0,
`max_grade` decimal(21,9) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee_link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:15,546 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standing_name` varchar(140),
`standing_color` varchar(140),
`min_grade` decimal(21,9) not null default 0,
`max_grade` decimal(21,9) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee_link` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:15,743 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`schedule_date` date,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`project_name` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:15,903 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item Supplied` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`reserve_warehouse` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`supplied_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`total_supplied_qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,066 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`naming_series` varchar(140),
`total_score` decimal(21,9) not null default 0,
`start_date` date,
`end_date` date,
`scorecard` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,187 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria_name` varchar(140),
`score` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`max_score` decimal(21,9) not null default 100.0,
`formula` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,310 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`variable_label` varchar(140),
`description` text,
`value` decimal(21,9) not null default 0,
`param_name` varchar(140),
`path` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,653 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`transaction_date` date,
`valid_till` date,
`quotation_number` varchar(140),
`has_unit_price_items` int(1) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) not null default 0,
`base_taxes_and_charges_deducted` decimal(21,9) not null default 0,
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`taxes_and_charges_added` decimal(21,9) not null default 0,
`taxes_and_charges_deducted` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`disable_rounded_total` int(1) not null default 0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`tc_name` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`auto_repeat` varchar(140),
`is_subcontracted` int(1) not null default 0,
`opportunity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `company`(`company`),
index `status`(`status`),
index `transaction_date`(`transaction_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,812 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Supplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`contact` varchar(140),
`quote_status` varchar(140),
`supplier_name` varchar(140),
`email_id` varchar(140),
`send_email` int(1) not null default 1,
`email_sent` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:16,962 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`supplier_score` varchar(140),
`indicator_color` varchar(140),
`status` varchar(140),
`period` varchar(140) default 'Per Month',
`weighting_function` text default '{total_score} * max( 0, min ( 1 , (12 - {period_number}) / 12) )',
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:17,232 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`lead_time_days` int(11) not null default 0,
`expected_delivery_date` date,
`is_free_item` int(1) not null default 0,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`prevdoc_doctype` varchar(140),
`material_request` varchar(140),
`sales_order` varchar(140),
`request_for_quotation` varchar(140),
`material_request_item` varchar(140),
`request_for_quotation_item` varchar(140),
`item_tax_rate` longtext,
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
index `material_request`(`material_request`),
index `sales_order`(`sales_order`),
index `material_request_item`(`material_request_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:17,482 WARNING database DDL Query made to DB:
create table `tabSupplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`supplier_name` varchar(140),
`country` varchar(140),
`supplier_group` varchar(140),
`supplier_type` varchar(140) default 'Company',
`is_transporter` int(1) not null default 0,
`image` text,
`default_currency` varchar(140),
`default_bank_account` varchar(140),
`default_price_list` varchar(140),
`is_internal_supplier` int(1) not null default 0,
`represents_company` varchar(140),
`supplier_details` text,
`website` varchar(140),
`language` varchar(140),
`tax_id` varchar(140),
`tax_category` varchar(140),
`tax_withholding_category` varchar(140),
`supplier_primary_address` varchar(140),
`primary_address` text,
`supplier_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`payment_terms` varchar(140),
`allow_purchase_invoice_creation_without_purchase_order` int(1) not null default 0,
`allow_purchase_invoice_creation_without_purchase_receipt` int(1) not null default 0,
`is_frozen` int(1) not null default 0,
`disabled` int(1) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`on_hold` int(1) not null default 0,
`hold_type` varchar(140),
`release_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:17,865 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) not null default 1.0,
`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`brand` varchar(140),
`product_bundle` varchar(140),
`schedule_date` date,
`expected_delivery_date` date,
`item_group` varchar(140),
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`subcontracted_quantity` decimal(21,9) not null default 0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`last_purchase_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`apply_tds` int(1) not null default 1,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`actual_qty` decimal(21,9) not null default 0,
`company_total_stock` decimal(21,9) not null default 0,
`material_request` varchar(140),
`material_request_item` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`sales_order_packed_item` varchar(140),
`supplier_quotation` varchar(140),
`supplier_quotation_item` varchar(140),
`delivered_by_supplier` int(1) not null default 0,
`against_blanket_order` int(1) not null default 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) not null default 0,
`received_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`is_fixed_asset` int(1) not null default 0,
`item_tax_rate` longtext,
`production_plan` varchar(140),
`production_plan_item` varchar(140),
`production_plan_sub_assembly_item` varchar(140),
`page_break` int(1) not null default 0,
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `sales_order`(`sales_order`),
index `sales_order_item`(`sales_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:17,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-07-14 16:19:18,018 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`variable_label` varchar(140) unique,
`is_custom` int(1) not null default 0,
`param_name` varchar(140) unique,
`path` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
