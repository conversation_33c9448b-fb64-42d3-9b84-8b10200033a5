set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 12:00:06.700144
Config  : ./mysite/private/backups/20250710_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 18:00:07.339341
Config  : ./mysite/private/backups/20250710_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 12:00:04.318513
Config  : ./mysite/private/backups/20250711_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 18:00:10.872747
Config  : ./mysite/private/backups/20250711_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_180003-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 12:00:05.704913
Config  : ./mysite/private/backups/20250713_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_120002-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 18:00:08.975396
Config  : ./mysite/private/backups/20250713_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_180003-mysite-database.sql.gz         1.4MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 12:00:06.280038
Config  : ./mysite/private/backups/20250714_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 18:00:05.452006
Config  : ./mysite/private/backups/20250714_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_180002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_000002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 00:00:05.272616
Config  : ./mysite/private/backups/20250715_000002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_000002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
