2025-07-09 18:53:00,392 DEBUG cd mybench && python3 -m venv env
2025-07-09 18:53:06,741 DEBUG cd mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade pip
2025-07-09 18:53:10,028 DEBUG cd mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet wheel
2025-07-09 18:53:13,721 LOG Getting frappe
2025-07-09 18:53:13,721 DEBUG cd mybench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-07-09 18:53:28,580 LOG Installing frappe
2025-07-09 18:53:28,583 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/frappe 
2025-07-09 18:55:05,020 DEBUG cd /home/<USER>/dev/mybench/apps/frappe && yarn install --check-files
2025-07-09 18:55:13,613 DEBUG cd mybench && bench build
2025-07-09 18:55:13,859 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-09 18:55:46,678 LOG setting up backups
2025-07-09 18:55:46,701 LOG backups were set up
2025-07-09 18:55:46,702 INFO Bench mybench initialized
2025-07-09 18:55:47,041 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 18:57:33,189 INFO /home/<USER>/dev/dev/bin/bench new-site mysite
2025-07-09 18:59:26,167 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 payments
2025-07-09 18:59:27,234 LOG Getting payments
2025-07-09 18:59:27,234 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git --branch version-15 --depth 1 --origin upstream
2025-07-09 18:59:29,191 LOG Installing payments
2025-07-09 18:59:29,191 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/payments 
2025-07-09 18:59:38,676 DEBUG bench build --app payments
2025-07-09 18:59:38,922 INFO /home/<USER>/dev/dev/bin/bench build --app payments
2025-07-09 18:59:42,130 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 18:59:52,787 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 erpnext
2025-07-09 18:59:53,654 LOG Getting erpnext
2025-07-09 18:59:53,654 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:00:04,820 LOG Installing erpnext
2025-07-09 19:00:04,821 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/erpnext 
2025-07-09 19:00:17,562 DEBUG cd /home/<USER>/dev/mybench/apps/erpnext && yarn install --check-files
2025-07-09 19:00:18,335 DEBUG bench build --app erpnext
2025-07-09 19:00:18,582 INFO /home/<USER>/dev/dev/bin/bench build --app erpnext
2025-07-09 19:00:24,488 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:00:52,045 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app payments
2025-07-09 19:01:08,887 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app erpnext
2025-07-09 19:02:59,698 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 hrms
2025-07-09 19:03:00,867 LOG Getting hrms
2025-07-09 19:03:00,867 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:03:04,901 LOG Installing hrms
2025-07-09 19:03:04,902 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/hrms 
2025-07-09 19:03:08,345 DEBUG cd /home/<USER>/dev/mybench/apps/hrms && yarn install --check-files
2025-07-09 19:03:34,150 DEBUG bench build --app hrms
2025-07-09 19:03:34,395 INFO /home/<USER>/dev/dev/bin/bench build --app hrms
2025-07-09 19:04:19,617 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:04:49,873 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/csf_tz.git
2025-07-09 19:04:49,887 LOG Getting csf_tz
2025-07-09 19:04:49,888 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:04:52,838 LOG Installing csf_tz
2025-07-09 19:04:52,838 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/csf_tz 
2025-07-09 19:05:24,379 DEBUG cd /home/<USER>/dev/mybench/apps/csf_tz && yarn install --check-files
2025-07-09 19:05:26,193 DEBUG bench build --app csf_tz
2025-07-09 19:05:26,433 INFO /home/<USER>/dev/dev/bin/bench build --app csf_tz
2025-07-09 19:05:30,360 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:06:08,464 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/PropMS.git
2025-07-09 19:06:08,479 LOG Getting PropMS
2025-07-09 19:06:08,479 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/PropMS.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:06:10,583 LOG Installing propms
2025-07-09 19:06:10,584 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/propms 
2025-07-09 19:06:18,800 DEBUG bench build --app propms
2025-07-09 19:06:19,044 INFO /home/<USER>/dev/dev/bin/bench build --app propms
2025-07-09 19:06:23,221 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:07:01,775 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/non_profit.git
2025-07-09 19:07:01,790 LOG Getting non_profit
2025-07-09 19:07:01,790 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:07:03,172 WARNING cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git --branch version-15 --depth 1 --origin upstream executed with exit code 128
2025-07-09 19:07:03,172 WARNING /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/non_profit.git executed with exit code 1
2025-07-09 19:07:03,572 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:07:11,878 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/non_profit.git
2025-07-09 19:07:11,892 LOG Getting non_profit
2025-07-09 19:07:11,892 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git --branch version-15 --depth 1 --origin upstream
2025-07-09 19:07:13,084 WARNING cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git --branch version-15 --depth 1 --origin upstream executed with exit code 128
2025-07-09 19:07:13,084 WARNING /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/non_profit.git executed with exit code 1
2025-07-09 19:07:13,811 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:07:25,853 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/Aakvatech-Limited/non_profit.git
2025-07-09 19:07:25,868 LOG Getting non_profit
2025-07-09 19:07:25,869 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git  --depth 1 --origin upstream
2025-07-09 19:07:27,818 LOG Installing non_profit
2025-07-09 19:07:27,818 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/non_profit 
2025-07-09 19:07:35,322 DEBUG bench build --app non_profit
2025-07-09 19:07:35,560 INFO /home/<USER>/dev/dev/bin/bench build --app non_profit
2025-07-09 19:07:39,731 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-09 19:07:58,353 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app hrms
2025-07-09 19:09:00,681 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app csf_tz
2025-07-09 19:10:50,806 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app propms
2025-07-09 19:11:31,600 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app non_profit
2025-07-09 19:13:32,591 INFO /home/<USER>/dev/dev/bin/bench --site mysite add-to-hosts
2025-07-09 19:15:40,267 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 19:15:40,752 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 19:15:40,752 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 19:15:40,754 INFO /home/<USER>/dev/dev/bin/bench serve --port 8003
2025-07-09 19:15:40,828 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 21:48:34,847 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 21:48:35,307 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 21:48:35,325 INFO /home/<USER>/dev/dev/bin/bench serve --port 8003
2025-07-09 21:48:35,333 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 21:48:35,368 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 21:49:53,017 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 21:49:53,684 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 21:49:53,696 INFO /home/<USER>/dev/dev/bin/bench serve --port 8003
2025-07-09 21:49:53,709 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 21:49:53,720 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 21:52:04,852 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 21:52:05,503 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 21:52:05,523 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-09 21:52:05,556 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 21:52:05,667 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 21:58:41,286 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 22:00:02,766 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 22:00:03,534 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 22:00:03,550 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-09 22:00:03,599 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 22:00:03,603 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 22:11:30,579 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 22:11:31,179 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 22:11:31,194 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-09 22:11:31,262 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 22:11:31,312 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-10 08:29:29,061 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-10 08:29:29,525 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-10 08:29:29,546 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-10 08:29:29,548 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-10 08:29:29,578 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-10 09:35:19,793 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app non_profit
2025-07-10 09:46:12,420 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/Aakvatech-Limited/fd.git
2025-07-10 09:46:12,447 LOG Getting fd
2025-07-10 09:46:12,447 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/fd.git  --depth 1 --origin upstream
2025-07-10 09:46:26,621 WARNING /home/<USER>/dev/dev/bin/bench get-app https://github.com/Aakvatech-Limited/fd.git executed with exit code 1
2025-07-10 09:46:27,612 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-10 09:46:37,843 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/Aakvatech-Limited/fd.git
2025-07-10 09:46:37,861 LOG Getting fd
2025-07-10 09:46:37,861 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/fd.git  --depth 1 --origin upstream
2025-07-10 09:46:41,129 LOG Installing fd
2025-07-10 09:46:41,130 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/fd 
2025-07-10 09:46:46,227 DEBUG bench build --app fd
2025-07-10 09:46:46,513 INFO /home/<USER>/dev/dev/bin/bench build --app fd
2025-07-10 09:46:52,701 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-10 09:47:08,589 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app fd
2025-07-10 09:47:31,952 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-10 09:47:32,456 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-10 09:47:32,463 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-10 09:47:32,468 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-10 09:47:32,549 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-10 12:00:01,489 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-10 18:00:01,888 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-11 08:52:46,966 INFO /home/<USER>/dev/dev/bin/bench new-app cv_maker
2025-07-11 08:52:46,977 LOG creating new app cv_maker
2025-07-11 08:57:29,547 LOG Installing cv_maker
2025-07-11 08:57:29,551 DEBUG cd /home/<USER>/dev/mybench && /home/<USER>/dev/mybench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/mybench/apps/cv_maker 
2025-07-11 08:57:34,261 DEBUG bench build --app cv_maker
2025-07-11 08:57:34,586 INFO /home/<USER>/dev/dev/bin/bench build --app cv_maker
2025-07-11 08:57:40,575 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-11 08:57:47,221 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-11 08:58:25,251 INFO /home/<USER>/dev/dev/bin/bench --site mysite make-doctype CV Skill
2025-07-11 08:58:32,556 INFO /home/<USER>/dev/dev/bin/bench --site mysite new-doctype CV Skill --app cv_maker
2025-07-11 08:58:39,137 INFO /home/<USER>/dev/dev/bin/bench --help
2025-07-11 09:09:31,175 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-11 09:09:39,942 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 09:09:40,438 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 09:09:40,474 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 09:09:40,533 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 09:09:40,533 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-11 09:10:06,769 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-11 09:33:01,211 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-11 09:33:27,465 INFO /home/<USER>/dev/dev/bin/bench build --app cv_maker
2025-07-11 12:00:01,985 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-11 15:09:49,974 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 15:10:06,503 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 15:12:41,294 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 15:12:42,196 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 15:12:42,270 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 15:12:42,701 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 15:12:42,879 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-11 18:00:01,736 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-13 07:00:56,867 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-13 07:00:57,595 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-13 07:00:57,624 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-13 07:00:57,648 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-13 07:00:57,657 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-13 07:22:08,896 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-13 07:24:13,505 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-13 07:25:47,957 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-13 07:25:48,555 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-13 07:25:48,557 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-13 07:25:48,590 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-13 07:25:48,594 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-13 07:25:51,769 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-13 07:27:52,650 INFO /home/<USER>/dev/dev/bin/bench --site mysite remove-from-nstalled-apps fd
2025-07-13 07:28:03,690 INFO /home/<USER>/dev/dev/bin/bench --site mysite remove-from-installed-apps fd
2025-07-13 07:28:10,236 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-13 07:55:41,408 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-13 08:00:01,215 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-13 08:03:51,100 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-13 08:14:18,467 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-13 08:31:20,261 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-13 12:00:01,396 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-13 18:00:02,231 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-13 20:55:07,312 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-13 20:55:07,975 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-13 20:55:07,998 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-13 20:55:08,002 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-13 20:55:08,013 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-13 20:55:16,428 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-13 21:02:44,813 INFO /home/<USER>/dev/dev/bin/bench set-config -g server_script_enabled 1
2025-07-13 21:02:49,673 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-13 21:02:50,311 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-13 21:02:50,321 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-13 21:02:50,402 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-13 21:02:50,513 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-13 21:52:02,216 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-13 21:53:07,413 INFO /home/<USER>/dev/dev/bin/bench --site mysite reinstall
2025-07-13 22:12:31,303 INFO /home/<USER>/dev/dev/bin/bench --site mysite reinstall
2025-07-14 08:55:05,429 INFO /home/<USER>/dev/dev/bin/bench --site mysite reinstall
2025-07-14 09:00:11,336 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app payments
2025-07-14 09:00:42,180 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app erpnext
2025-07-14 09:03:00,225 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app hrms
2025-07-14 09:04:15,129 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app csf_tz
2025-07-14 09:11:58,151 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app propms
2025-07-14 09:12:40,933 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app non_profit
2025-07-14 09:14:20,265 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app fd
2025-07-14 09:14:42,809 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-14 09:21:03,012 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-14 09:21:33,309 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 09:21:58,168 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 09:21:58,680 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 09:21:58,704 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 09:21:58,724 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 09:21:58,737 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 09:22:30,312 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-14 09:22:47,428 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 09:23:40,797 INFO /home/<USER>/dev/dev/bin/bench --site mysite clear-cache
2025-07-14 09:23:52,080 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-14 09:27:10,705 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 09:27:23,319 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 09:27:23,899 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 09:27:23,906 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 09:27:23,909 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 09:27:23,910 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 09:39:31,658 INFO /home/<USER>/dev/dev/bin/bench --site mysite execute cv_maker.setup_master_data.setup_master_data
2025-07-14 09:40:33,971 INFO /home/<USER>/dev/dev/bin/bench --site mysite execute cv_maker.create_sample_data.create_sample_data
2025-07-14 09:41:43,947 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 09:42:09,938 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 09:42:10,441 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 09:42:10,460 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 09:42:10,463 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 09:42:10,561 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 09:43:08,956 INFO /home/<USER>/dev/dev/bin/bench --site mysite execute cv_maker.create_sample_data.create_sample_data
2025-07-14 09:45:12,756 INFO /home/<USER>/dev/dev/bin/bench --site mysite execute cv_maker.create_sample_data.create_sample_data
2025-07-14 09:47:55,808 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 09:48:23,825 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-14 09:50:02,673 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 09:50:35,115 INFO /home/<USER>/dev/dev/bin/bench --site mysite execute cv_maker.create_sample_data.create_sample_data
2025-07-14 10:21:56,931 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:22:29,410 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:22:30,052 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 10:22:30,065 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 10:22:30,083 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:22:30,087 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 10:24:32,233 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:24:32,913 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 10:24:32,913 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:24:32,964 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 10:24:32,974 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 10:24:46,538 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:24:47,139 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 10:24:47,148 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 10:24:47,191 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:24:47,193 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 10:24:59,849 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:42:16,342 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-14 10:51:36,092 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:51:36,758 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 10:51:36,769 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 10:51:36,842 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:51:37,001 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 10:59:26,261 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 10:59:26,871 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 10:59:26,880 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 10:59:26,884 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 10:59:26,886 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 12:00:01,511 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-14 12:17:37,890 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 12:17:38,473 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 12:17:38,478 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 12:17:38,499 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 12:17:38,500 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 12:18:38,766 INFO /home/<USER>/dev/dev/bin/bench use mysite
2025-07-14 12:18:43,877 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 12:18:44,466 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 12:18:44,478 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 12:18:44,485 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 12:18:44,506 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 14:33:46,170 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 15:19:39,893 INFO /home/<USER>/dev/dev/bin/bench --site mysite remove-from-installed-apps fd
2025-07-14 15:19:46,350 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 16:06:15,501 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 16:10:16,950 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-14 16:15:40,537 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 16:16:27,345 INFO /home/<USER>/dev/dev/bin/bench --site mysite remove-from-installed-apps cv_manager
2025-07-14 16:16:30,308 INFO /home/<USER>/dev/dev/bin/bench --site mysite remove-from-installed-apps cv_manager
2025-07-14 16:16:33,590 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 16:17:06,201 INFO /home/<USER>/dev/dev/bin/bench --site mysite reinstall
2025-07-14 16:29:15,536 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app csf_tz
2025-07-14 16:30:50,161 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app propms
2025-07-14 16:40:32,201 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 16:40:32,797 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 16:40:32,800 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 16:40:32,812 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 16:40:32,821 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 16:51:12,425 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 17:01:00,218 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-14 17:03:06,819 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-14 17:04:47,755 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 17:04:48,561 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 17:04:48,608 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 17:04:48,609 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 17:04:48,872 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 17:09:03,264 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 17:12:13,066 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-14 18:00:01,428 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-14 18:24:54,945 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-14 18:24:55,645 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-14 18:24:55,682 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-14 18:24:55,742 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-14 18:24:55,745 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-14 18:56:45,479 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-15 00:00:01,416 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-15 09:00:45,916 INFO /home/<USER>/dev/dev/bin/bench new-site test-site
2025-07-15 09:01:53,312 INFO /home/<USER>/dev/dev/bin/bench --site test-site install-app erpnext
2025-07-15 09:03:36,471 INFO /home/<USER>/dev/dev/bin/bench --site test-site install-app propms
2025-07-15 09:04:06,588 INFO /home/<USER>/dev/dev/bin/bench --site test-site add-to-hosts
2025-07-15 09:04:28,168 INFO /home/<USER>/dev/dev/bin/bench use test-site
2025-07-15 09:04:38,687 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-15 09:04:39,274 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-15 09:04:39,286 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-15 09:04:39,286 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-15 09:04:39,310 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-15 09:06:23,480 INFO /home/<USER>/dev/dev/bin/bench --site test-site migrate
2025-07-15 09:51:38,593 INFO /home/<USER>/dev/dev/bin/bench use mysite
2025-07-15 09:51:45,122 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-15 09:51:45,760 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-15 09:51:45,760 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-15 09:51:45,762 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-15 09:51:45,762 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-15 12:00:01,412 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-15 13:57:49,010 INFO /home/<USER>/dev/dev/bin/bench --site test-site migrate
2025-07-15 13:58:11,510 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-15 13:59:39,338 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-15 14:02:27,972 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-15 14:03:41,225 INFO /home/<USER>/dev/dev/bin/bench --site mysite install-app cv_maker
2025-07-15 14:03:56,278 INFO /home/<USER>/dev/dev/bin/bench --site mysite console
2025-07-15 14:09:28,169 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-15 14:09:28,842 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-15 14:09:28,866 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-15 14:09:28,902 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-15 14:09:28,995 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
