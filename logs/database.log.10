2025-07-14 16:20:29,519 WARNING database DDL Query made to DB:
create table `tabShift Schedule Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`shift_schedule` varchar(140),
`shift_location` varchar(140),
`shift_status` varchar(140) default 'Active',
`enabled` int(1) not null default 1,
`create_shifts_after` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:29,684 WARNING database DDL Query made to DB:
create table `tabTraining Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_event` varchar(140) unique,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:29,900 WARNING database DDL Query made to DB:
create table `tabLeave Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type_name` varchar(140) unique,
`max_leaves_allowed` decimal(21,9) not null default 0,
`applicable_after` int(11) not null default 0,
`max_continuous_days_allowed` int(11) not null default 0,
`is_carry_forward` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`is_ppl` int(1) not null default 0,
`fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0,
`is_optional_leave` int(1) not null default 0,
`allow_negative` int(1) not null default 0,
`allow_over_allocation` int(1) not null default 0,
`include_holiday` int(1) not null default 0,
`is_compensatory` int(1) not null default 0,
`maximum_carry_forwarded_leaves` decimal(21,9) not null default 0,
`expire_carry_forwarded_leaves_after_days` int(11) not null default 0,
`allow_encashment` int(1) not null default 0,
`max_encashable_leaves` int(11) not null default 0,
`non_encashable_leaves` int(11) not null default 0,
`earning_component` varchar(140),
`is_earned_leave` int(1) not null default 0,
`earned_leave_frequency` varchar(140),
`allocate_on_day` varchar(140) default 'Last Day',
`rounding` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:30,069 WARNING database DDL Query made to DB:
create table `tabEmployee Grade` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_salary_structure` varchar(140),
`currency` varchar(140),
`default_base_pay` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:30,405 WARNING database DDL Query made to DB:
create table `tabGrievance Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:30,622 WARNING database DDL Query made to DB:
create table `tabVehicle Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`license_plate` varchar(140),
`employee` varchar(140),
`model` varchar(140),
`make` varchar(140),
`date` date,
`odometer` int(11) not null default 0,
`last_odometer` int(11) not null default 0,
`fuel_qty` decimal(21,9) not null default 0,
`price` decimal(21,9) not null default 0,
`supplier` varchar(140),
`invoice` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:30,962 WARNING database DDL Query made to DB:
create table `tabJob Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_name` varchar(140),
`email_id` varchar(140),
`phone_number` varchar(140),
`country` varchar(140),
`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`source` varchar(140),
`source_name` varchar(140),
`employee_referral` varchar(140),
`applicant_rating` decimal(3,2),
`notes` varchar(140),
`cover_letter` text,
`resume_attachment` text,
`resume_link` varchar(140),
`currency` varchar(140),
`lower_range` decimal(21,9) not null default 0,
`upper_range` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `job_title`(`job_title`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,109 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,294 WARNING database DDL Query made to DB:
create table `tabTraining Result Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`hours` decimal(21,9) not null default 0,
`grade` varchar(140),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,457 WARNING database DDL Query made to DB:
create table `tabEmployee Skill Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140) unique,
`employee_name` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,597 WARNING database DDL Query made to DB:
create table `tabSkill Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,760 WARNING database DDL Query made to DB:
create table `tabTraining Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_program` varchar(140),
`status` varchar(140) default 'Scheduled',
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`description` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:31,925 WARNING database DDL Query made to DB:
create table `tabAppointment Letter Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:32,157 WARNING database DDL Query made to DB:
create table `tabLeave Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`company` varchar(140),
`department` varchar(140),
`from_date` date,
`to_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`total_leave_days` decimal(21,9) not null default 0,
`description` text,
`leave_balance` decimal(21,9) not null default 0,
`leave_approver` varchar(140),
`leave_approver_name` varchar(140),
`follow_via_email` int(1) not null default 1,
`posting_date` date,
`status` varchar(140) default 'Open',
`salary_slip` varchar(140),
`color` varchar(140),
`letter_head` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `leave_type`(`leave_type`),
index `from_date`(`from_date`),
index `to_date`(`to_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:32,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application`
				ADD INDEX IF NOT EXISTS `employee_from_date_to_date_index`(employee, from_date, to_date)
2025-07-14 16:20:32,405 WARNING database DDL Query made to DB:
create table `tabEmployee Promotion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`salary_currency` varchar(140),
`promotion_date` date,
`company` varchar(140),
`current_ctc` decimal(21,9) not null default 0,
`revised_ctc` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:32,558 WARNING database DDL Query made to DB:
create table `tabDepartment Approver` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`approver` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:32,776 WARNING database DDL Query made to DB:
create table `tabEmployee Grievance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`employee_name` varchar(140),
`designation` varchar(140),
`date` date,
`status` varchar(140) default 'Open',
`reports_to` varchar(140),
`grievance_against_party` varchar(140),
`grievance_against` varchar(140),
`grievance_type` varchar(140),
`associated_document_type` varchar(140),
`associated_document` varchar(140),
`description` text,
`cause_of_grievance` text,
`resolved_by` varchar(140),
`resolution_date` date,
`employee_responsible` varchar(140),
`resolution_detail` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:32,964 WARNING database DDL Query made to DB:
create table `tabExpense Claim Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`deferred_expense_account` int(1) not null default 0,
`expense_type` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:33,139 WARNING database DDL Query made to DB:
create table `tabTraining Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`course` varchar(140),
`training_event` varchar(140),
`event_name` varchar(140),
`trainer_name` varchar(140),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:33,287 WARNING database DDL Query made to DB:
create table `tabAppointment Letter content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:33,608 WARNING database DDL Query made to DB:
create table `tabJob Applicant Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:33,790 WARNING database DDL Query made to DB:
create table `tabTravel Itinerary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`travel_from` varchar(140),
`travel_to` varchar(140),
`mode_of_travel` varchar(140),
`meal_preference` varchar(140),
`travel_advance_required` int(1) not null default 0,
`advance_amount` varchar(140),
`departure_date` datetime(6),
`arrival_date` datetime(6),
`lodging_required` int(1) not null default 0,
`preferred_area_for_lodging` varchar(140),
`check_in_date` date,
`check_out_date` date,
`other_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:33,972 WARNING database DDL Query made to DB:
create table `tabLeave Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`transaction_type` varchar(140),
`transaction_name` varchar(140),
`company` varchar(140),
`leaves` decimal(21,9) not null default 0,
`from_date` date,
`to_date` date,
`holiday_list` varchar(140),
`is_carry_forward` int(1) not null default 0,
`is_expired` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `transaction_type`(`transaction_type`),
index `transaction_name`(`transaction_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:34,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry`
				ADD INDEX IF NOT EXISTS `transaction_type_transaction_name_index`(transaction_type, transaction_name)
2025-07-14 16:20:34,201 WARNING database DDL Query made to DB:
create table `tabShift Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`shift_type` varchar(140),
`shift_location` varchar(140),
`status` varchar(140) default 'Active',
`start_date` date,
`end_date` date,
`shift_request` varchar(140),
`shift_schedule_assignment` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift_type`(`shift_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:34,367 WARNING database DDL Query made to DB:
create table `tabInterest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interest` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:34,535 WARNING database DDL Query made to DB:
create table `tabExpense Claim Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`default_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:34,694 WARNING database DDL Query made to DB:
create table `tabAppointment Letter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`company` varchar(140),
`appointment_date` date,
`appointment_letter_template` varchar(140),
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:34,960 WARNING database DDL Query made to DB:
create table `tabExit Interview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`email` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`department` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`ref_doctype` varchar(140),
`questionnaire_email_sent` int(1) not null default 0,
`reference_document_name` varchar(140),
`interview_summary` longtext,
`employee_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,107 WARNING database DDL Query made to DB:
create table `tabInterviewer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,287 WARNING database DDL Query made to DB:
create table `tabEmployee Separation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`resignation_letter_date` date,
`boarding_begins_on` date,
`project` varchar(140),
`employee_separation_template` varchar(140),
`notify_users_by_email` int(1) not null default 0,
`exit_interview` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,431 WARNING database DDL Query made to DB:
create table `tabIdentification Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`identification_document_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,585 WARNING database DDL Query made to DB:
create table `tabStaffing Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`vacancies` int(11) not null default 0,
`estimated_cost_per_position` decimal(21,9) not null default 0,
`total_estimated_cost` decimal(21,9) not null default 0,
`current_count` int(11) not null default 0,
`current_openings` int(11) not null default 0,
`number_of_positions` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,778 WARNING database DDL Query made to DB:
create table `tabTravel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`travel_type` varchar(140),
`travel_funding` varchar(140),
`travel_proof` text,
`purpose_of_travel` varchar(140),
`details_of_sponsor` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`cell_number` varchar(140),
`prefered_email` varchar(140),
`company` varchar(140),
`date_of_birth` date,
`personal_id_type` varchar(140),
`personal_id_number` varchar(140),
`passport_number` varchar(140),
`description` text,
`cost_center` varchar(140),
`name_of_organizer` varchar(140),
`address_of_organizer` varchar(140),
`other_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:35,914 WARNING database DDL Query made to DB:
create table `tabDesignation Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:36,069 WARNING database DDL Query made to DB:
create table `tabFull and Final Outstanding Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`component` varchar(140),
`reference_document_type` varchar(140),
`reference_document` varchar(140),
`account` varchar(140),
`paid_via_salary_slip` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`status` varchar(140) default 'Unsettled',
`remark` text,
index `reference_document`(`reference_document`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:36,207 WARNING database DDL Query made to DB:
create table `tabKRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:36,572 WARNING database DDL Query made to DB:
create table `tabGoal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal_name` varchar(140),
`is_group` int(1) not null default 0,
`parent_goal` varchar(140),
`progress` decimal(21,9) not null default 0,
`status` varchar(140) default 'Pending',
`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`appraisal_cycle` varchar(140),
`kra` varchar(140),
`description` longtext,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:36,744 WARNING database DDL Query made to DB:
create table `tabJob Offer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:36,890 WARNING database DDL Query made to DB:
create table `tabFull and Final Asset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference` varchar(140),
`asset_name` varchar(140),
`date` datetime(6),
`actual_cost` decimal(21,9) not null default 0,
`cost` decimal(21,9) not null default 0,
`account` varchar(140),
`action` varchar(140) default 'Return',
`status` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,078 WARNING database DDL Query made to DB:
create table `tabJob Requisition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`designation` varchar(140),
`department` varchar(140),
`no_of_positions` int(11) not null default 0,
`expected_compensation` decimal(21,9) not null default 0,
`company` varchar(140),
`status` varchar(140),
`requested_by` varchar(140),
`requested_by_name` varchar(140),
`requested_by_dept` varchar(140),
`requested_by_designation` varchar(140),
`posting_date` date,
`completed_on` date,
`expected_by` date,
`time_to_fill` decimal(21,9),
`description` longtext,
`reason_for_requesting` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,224 WARNING database DDL Query made to DB:
create table `tabEmployee Property History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`current` varchar(140),
`new` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,424 WARNING database DDL Query made to DB:
create table `tabLeave Encashment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_period` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`leave_balance` decimal(21,9) not null default 0,
`actual_encashable_days` decimal(21,9) not null default 0,
`encashment_days` decimal(21,9) not null default 0,
`encashment_amount` decimal(21,9) not null default 0,
`pay_via_payment_entry` int(1) not null default 0,
`expense_account` varchar(140),
`payable_account` varchar(140),
`posting_date` date,
`currency` varchar(140),
`paid_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`encashment_date` date,
`additional_salary` varchar(140),
`amended_from` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,611 WARNING database DDL Query made to DB:
create table `tabJob Offer Term Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,755 WARNING database DDL Query made to DB:
create table `tabEmployee Training` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training` varchar(140),
`training_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:37,893 WARNING database DDL Query made to DB:
create table `tabVehicle Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,052 WARNING database DDL Query made to DB:
create table `tabVehicle Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140),
`type` varchar(140),
`frequency` varchar(140),
`expense_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,233 WARNING database DDL Query made to DB:
create table `tabShift Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_time` time(6),
`end_time` time(6),
`holiday_list` varchar(140),
`color` varchar(140) default 'Blue',
`enable_auto_attendance` int(1) not null default 0,
`determine_check_in_and_check_out` varchar(140),
`working_hours_calculation_based_on` varchar(140),
`begin_check_in_before_shift_start_time` int(11) not null default 60,
`allow_check_out_after_shift_end_time` int(11) not null default 60,
`mark_auto_attendance_on_holidays` int(1) not null default 0,
`working_hours_threshold_for_half_day` decimal(21,9) not null default 0,
`working_hours_threshold_for_absent` decimal(21,9) not null default 0,
`process_attendance_after` date,
`last_sync_of_checkin` datetime(6),
`auto_update_last_sync` int(1) not null default 0,
`enable_late_entry_marking` int(1) not null default 0,
`late_entry_grace_period` int(11) not null default 0,
`enable_early_exit_marking` int(1) not null default 0,
`early_exit_grace_period` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,392 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,565 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-07-14 16:20:38,587 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` int(1) not null default 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,755 WARNING database DDL Query made to DB:
create table `tabLeave Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_active` int(1) not null default 0,
`company` varchar(140),
`optional_holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:38,934 WARNING database DDL Query made to DB:
create table `tabExpense Claim Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_advance` varchar(140),
`posting_date` date,
`advance_paid` decimal(21,9) not null default 0,
`unclaimed_amount` decimal(21,9) not null default 0,
`return_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`advance_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:39,076 WARNING database DDL Query made to DB:
create table `tabEmployment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_type_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:39,263 WARNING database DDL Query made to DB:
create table `tabCompensatory Leave Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`work_from_date` date,
`work_end_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:39,449 WARNING database DDL Query made to DB:
create table `tabEmployee Health Insurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`health_insurance_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:39,664 WARNING database DDL Query made to DB:
create table `tabInterview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview_round` varchar(140),
`job_applicant` varchar(140),
`job_opening` varchar(140),
`designation` varchar(140),
`resume_link` varchar(140),
`status` varchar(140) default 'Pending',
`scheduled_on` date,
`from_time` time(6),
`to_time` time(6),
`expected_average_rating` decimal(3,2),
`average_rating` decimal(3,2),
`interview_summary` text,
`reminded` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:39,858 WARNING database DDL Query made to DB:
create table `tabShift Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`approver` varchar(140),
`from_date` date,
`to_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:40,059 WARNING database DDL Query made to DB:
create table `tabEmployee Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transfer_date` date,
`company` varchar(140),
`new_company` varchar(140),
`department` varchar(140),
`reallocate_leaves` int(1) not null default 0,
`create_new_employee_id` int(1) not null default 0,
`new_employee_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:40,237 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:40,419 WARNING database DDL Query made to DB:
create table `tabAppraisal Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:40,631 WARNING database DDL Query made to DB:
create table `tabAttendance Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`from_date` date,
`to_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`include_holidays` int(1) not null default 0,
`shift` varchar(140),
`reason` varchar(140),
`explanation` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:40,841 WARNING database DDL Query made to DB:
create table `tabInterview Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview` varchar(140),
`interview_round` varchar(140),
`job_applicant` varchar(140),
`interviewer` varchar(140),
`result` varchar(140),
`average_rating` decimal(3,2),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,048 WARNING database DDL Query made to DB:
create table `tabEmployee Performance Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`company` varchar(140),
`reviewer` varchar(140),
`reviewer_name` varchar(140),
`reviewer_designation` varchar(140),
`user` varchar(140),
`added_on` datetime(6),
`appraisal_cycle` varchar(140),
`appraisal` varchar(140),
`total_score` decimal(21,9) not null default 0,
`feedback` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,238 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Rating` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,406 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,586 WARNING database DDL Query made to DB:
create table `tabLeave Block List Allow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allow_user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,787 WARNING database DDL Query made to DB:
create table `tabEmployee Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`date` date,
`status` varchar(140),
`for_designation` varchar(140),
`email` varchar(140) unique,
`contact_no` varchar(140),
`resume_link` varchar(140),
`current_employer` varchar(140),
`current_job_title` varchar(140),
`resume` text,
`referrer` varchar(140),
`referrer_name` varchar(140),
`is_applicable_for_referral_bonus` int(1) not null default 1,
`referral_payment_status` varchar(140),
`department` varchar(140),
`qualification_reason` longtext,
`work_references` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:41,967 WARNING database DDL Query made to DB:
create table `tabEmployee Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`proficiency` decimal(3,2),
`evaluation_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:42,136 WARNING database DDL Query made to DB:
create table `tabExpense Claim Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_date` date,
`expense_type` varchar(140),
`default_account` varchar(140),
`description` longtext,
`amount` decimal(21,9) not null default 0,
`sanctioned_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:43,917 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:44,057 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:44,223 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:44,422 WARNING database DDL Query made to DB:
create table `tabAdditional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`is_recurring` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_date` date,
`to_date` date,
`payroll_date` date,
`amended_from` varchar(140),
`salary_component` varchar(140),
`type` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`overwrite_salary_structure_amount` int(1) not null default 1,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `payroll_date`(`payroll_date`),
index `salary_component`(`salary_component`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:44,693 WARNING database DDL Query made to DB:
create table `tabSalary Structure Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`salary_structure` varchar(140),
`from_date` date,
`income_tax_slab` varchar(140),
`company` varchar(140),
`payroll_payable_account` varchar(140),
`currency` varchar(140),
`base` decimal(21,9) not null default 0,
`variable` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`taxable_earnings_till_date` decimal(21,9) not null default 0,
`tax_deducted_till_date` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `salary_structure`(`salary_structure`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:44,887 WARNING database DDL Query made to DB:
create table `tabEmployee Other Income` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`source` varchar(140),
`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `company`(`company`),
index `payroll_period`(`payroll_period`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:45,086 WARNING database DDL Query made to DB:
create table `tabEmployee Incentive` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`department` varchar(140),
`salary_component` varchar(140),
`currency` varchar(140),
`payroll_date` date,
`incentive_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:45,270 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`total_declared_amount` decimal(21,9) not null default 0,
`total_exemption_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:45,480 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:45,701 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`claim_date` date,
`currency` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`earning_component` varchar(140),
`max_amount_eligible` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`salary_slip` varchar(140),
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:45,880 WARNING database DDL Query made to DB:
create table `tabPayroll Employee Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`is_salary_withheld` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:46,019 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`type_of_proof` varchar(140),
`attach_proof` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:46,241 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:46,405 WARNING database DDL Query made to DB:
create table `tabPayroll Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:46,602 WARNING database DDL Query made to DB:
create table `tabGratuity Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable` int(1) not null default 0,
`calculate_gratuity_amount_based_on` varchar(140),
`total_working_days_per_year` int(11) not null default 365,
`work_experience_calculation_function` varchar(140) default 'Round off Work Experience',
`minimum_year_for_gratuity` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:46,850 WARNING database DDL Query made to DB:
create table `tabSalary Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`abbr` varchar(140),
`amount` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
`is_recurring_additional_salary` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`depends_on_payment_days` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`is_tax_applicable` int(1) not null default 0,
`is_flexible_benefit` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`condition` longtext,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`default_amount` decimal(21,9) not null default 0,
`additional_amount` decimal(21,9) not null default 0,
`tax_on_flexible_benefit` decimal(21,9) not null default 0,
`tax_on_additional_salary` decimal(21,9) not null default 0,
index `salary_component`(`salary_component`),
index `exempted_from_income_tax`(`exempted_from_income_tax`),
index `is_tax_applicable`(`is_tax_applicable`),
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:47,000 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab Other Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`percent` decimal(21,9) not null default 0,
`min_taxable_income` decimal(21,9) not null default 0,
`max_taxable_income` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:47,134 WARNING database DDL Query made to DB:
create table `tabPayroll Period Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:47,480 WARNING database DDL Query made to DB:
create table `tabRetention Bonus` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`date_of_joining` varchar(140),
`salary_component` varchar(140),
`bonus_amount` decimal(21,9) not null default 0,
`bonus_payment_date` date,
`currency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:47,667 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`earning_component` varchar(140),
`pay_against_benefit_claim` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:47,902 WARNING database DDL Query made to DB:
create table `tabPayroll Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`company` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`payroll_payable_account` varchar(140),
`status` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`number_of_employees` int(11) not null default 0,
`validate_attendance` int(1) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
`payment_account` varchar(140),
`bank_account` varchar(140),
`salary_slips_created` int(1) not null default 0,
`salary_slips_submitted` int(1) not null default 0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:48,218 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:48,366 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) not null default 0,
`expired_leaves` decimal(21,9) not null default 0,
`used_leaves` decimal(21,9) not null default 0,
`pending_leaves` decimal(21,9) not null default 0,
`available_leaves` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:48,555 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) not null default 0,
`remaining_benefit` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`pro_rata_dispensed_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:48,764 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`submission_date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`total_actual_amount` decimal(21,9) not null default 0,
`exemption_amount` decimal(21,9) not null default 0,
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,016 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140) unique,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` int(1) not null default 1,
`is_tax_applicable` int(1) not null default 1,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`is_income_tax_component` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`round_to_the_nearest_integer` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`remove_if_zero_valued` int(1) not null default 1,
`disabled` int(1) not null default 0,
`condition` longtext,
`amount` decimal(21,9) not null default 0,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`is_flexible_benefit` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`only_tax_impact` int(1) not null default 0,
`create_separate_payment_entry_against_benefit_claim` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,210 WARNING database DDL Query made to DB:
create table `tabSalary Slip Loan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_payment` decimal(21,9) not null default 0,
`loan_repayment_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,376 WARNING database DDL Query made to DB:
create table `tabEmployee Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_center` varchar(140),
`percentage` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,517 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,660 WARNING database DDL Query made to DB:
create table `tabTaxable Salary Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_amount` decimal(21,9) not null default 0,
`to_amount` decimal(21,9) not null default 0,
`percent_deduction` decimal(21,9) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:49,864 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:50,043 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:50,407 WARNING database DDL Query made to DB:
create table `tabSalary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
`posting_date` date,
`letter_head` varchar(140),
`status` varchar(140),
`salary_withholding` varchar(140),
`salary_withholding_cycle` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`salary_structure` varchar(140),
`payroll_entry` varchar(140),
`mode_of_payment` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`total_working_days` decimal(21,9) not null default 0,
`unmarked_days` decimal(21,9) not null default 0,
`leave_without_pay` decimal(21,9) not null default 0,
`absent_days` decimal(21,9) not null default 0,
`payment_days` decimal(21,9) not null default 0,
`total_working_hours` decimal(21,9) not null default 0,
`hour_rate` decimal(21,9) not null default 0,
`base_hour_rate` decimal(21,9) not null default 0,
`gross_pay` decimal(21,9) not null default 0,
`base_gross_pay` decimal(21,9) not null default 0,
`gross_year_to_date` decimal(21,9) not null default 0,
`base_gross_year_to_date` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`base_total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`base_net_pay` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`base_year_to_date` decimal(21,9) not null default 0,
`month_to_date` decimal(21,9) not null default 0,
`base_month_to_date` decimal(21,9) not null default 0,
`total_in_words` varchar(240),
`base_total_in_words` varchar(240),
`ctc` decimal(21,9) not null default 0,
`income_from_other_sources` decimal(21,9) not null default 0,
`total_earnings` decimal(21,9) not null default 0,
`non_taxable_earnings` decimal(21,9) not null default 0,
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`tax_exemption_declaration` decimal(21,9) not null default 0,
`deductions_before_tax_calculation` decimal(21,9) not null default 0,
`annual_taxable_amount` decimal(21,9) not null default 0,
`income_tax_deducted_till_date` decimal(21,9) not null default 0,
`current_month_income_tax` decimal(21,9) not null default 0,
`future_income_tax_deductions` decimal(21,9) not null default 0,
`total_income_tax` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`amended_from` varchar(140),
`bank_name` varchar(140),
`bank_account_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `salary_structure`(`salary_structure`),
index `payroll_entry`(`payroll_entry`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:50,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-07-14 16:20:50,615 WARNING database DDL Query made to DB:
create table `tabSalary Slip Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_sheet` varchar(140),
`working_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:50,801 WARNING database DDL Query made to DB:
create table `tabGratuity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`current_work_experience` decimal(21,9) not null default 0,
`posting_date` date,
`gratuity_rule` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`pay_via_salary_slip` int(1) not null default 1,
`amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`payroll_date` date,
`salary_component` varchar(140),
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`expense_account` varchar(140),
`payable_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:50,961 WARNING database DDL Query made to DB:
create table `tabSalary Component Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:53,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-07-14 16:20:53,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0
2025-07-14 16:20:53,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-07-14 16:20:53,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-14 16:20:53,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-07-14 16:20:53,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-14 16:20:53,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-07-14 16:20:53,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-07-14 16:20:53,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-14 16:20:54,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-07-14 16:20:54,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `base_total_costing_amount` decimal(21,9) not null default 0, MODIFY `base_total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_billed_hours` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_billable_hours` decimal(21,9) not null default 0, MODIFY `base_total_billable_amount` decimal(21,9) not null default 0
2025-07-14 16:20:54,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-07-14 16:20:54,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` int(1) not null default 1
2025-07-14 16:29:20,206 WARNING database DDL Query made to DB:
create table `tabPiecework Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task_code` varchar(140) unique,
`task_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
`disabled` int(1) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:20,375 WARNING database DDL Query made to DB:
create table `tabSalary Slip OT Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:20,542 WARNING database DDL Query made to DB:
create table `tabAV Report Extension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140) unique,
`active` int(1) not null default 0,
`script` longtext,
`html_format` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:20,762 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Cover Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle` varchar(140),
`covernotenumber` varchar(140) unique,
`covernotestartdate` varchar(140),
`covernoteenddate` varchar(140),
`stickernumber` varchar(140),
`covernotereferencenumber` varchar(140),
`productname` varchar(140),
`classofbusiness` varchar(140),
`transactingcompany` varchar(140),
`transactingcompanytype` varchar(140),
`covernotedescription` varchar(1000),
`officername` varchar(140),
`ismotor` int(1) not null default 0,
`isfleet` int(1) not null default 0,
`statustitle` varchar(140),
`currencycode` varchar(140),
`totalpremiumamountexcludingtax` decimal(21,9) not null default 0,
`totalpremiumamountincludingtax` decimal(21,9) not null default 0,
`commisionrate` decimal(21,9) not null default 0,
`exchangerate` decimal(21,9) not null default 0,
`commisionpaid` decimal(21,9) not null default 0,
`vat` varchar(140),
`operativeclause` varchar(140),
`officertitle` varchar(140),
`fleetidentificationnumber` varchar(140),
`fleetsize` varchar(140),
`premiumlevy` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:20,915 WARNING database DDL Query made to DB:
create table `tabTZ District` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`district` varchar(140) unique,
`region` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:21,065 WARNING database DDL Query made to DB:
create table `tabRepack Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_target_warehouse` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_uom` varchar(140),
`qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:21,212 WARNING database DDL Query made to DB:
create table `tabElectronic Fiscal Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`serial_no` varchar(140),
`location` varchar(140),
`supplier` varchar(140),
`make` varchar(140),
`model` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:21,494 WARNING database DDL Query made to DB:
create table `tabAttachment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:21,662 WARNING database DDL Query made to DB:
create table `tabPrice Change Request Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`price_list` varchar(140),
`old_price` decimal(21,9) not null default 0,
`item_name` varchar(140),
`cost` decimal(21,9) not null default 0,
`new_price` decimal(21,9) not null default 0,
`valid_from` date,
`valid_to` date,
`price_list_currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:21,824 WARNING database DDL Query made to DB:
create table `tabCSF TZ Bank Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`bank_account` varchar(140),
`currency` varchar(140),
`company` varchar(140),
`account` varchar(140),
`bank_supplier` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`total_bank_charges` decimal(21,9) not null default 0,
`ref_pi` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:22,006 WARNING database DDL Query made to DB:
create table `tabInter Company Stock Transfer Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:22,294 WARNING database DDL Query made to DB:
create table `tabBackground Document Posting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`posting_type` varchar(140),
`timeout` int(11) not null default 600,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:22,458 WARNING database DDL Query made to DB:
create table `tabSingle Piecework Employees` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`task_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
