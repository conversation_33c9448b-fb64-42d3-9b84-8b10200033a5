2025-07-15 09:03:22,604 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`schedule_date` date,
`expected_delivery_date` date,
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 1.0,
`received_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`expense_account` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`purchase_order_item` varchar(140),
`page_break` int(1) not null default 0,
`subcontracting_conversion_factor` decimal(21,9) not null default 0,
`job_card` varchar(140),
index `item_code`(`item_code`),
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:22,886 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`bill_no` varchar(140),
`bill_date` date,
`supplier_address` varchar(140),
`contact_person` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`range` varchar(140),
`represents_company` varchar(140),
`status` varchar(140) default 'Draft',
`per_returned` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`language` varchar(140),
`instructions` text,
`select_print_heading` varchar(140),
`remarks` text,
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:23,104 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`available_qty_for_consumption` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`current_stock` decimal(21,9) not null default 0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`subcontracting_order` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:23,257 WARNING database DDL Query made to DB:
create table `tabCode List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`canonical_uri` varchar(140),
`url` varchar(140),
`default_common_code` varchar(140),
`version` varchar(140),
`publisher` varchar(140),
`publisher_id` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:23,410 WARNING database DDL Query made to DB:
create table `tabCommon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_list` varchar(140),
`title` varchar(300),
`common_code` varchar(300),
`description` text,
`additional_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_list`(`code_list`),
index `common_code`(`common_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:23,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommon Code`
				ADD INDEX IF NOT EXISTS `code_list_common_code_index`(code_list, common_code)
2025-07-15 09:03:31,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` ADD COLUMN `tax_category` varchar(140), ADD COLUMN `is_your_company_address` int(1) not null default 0
2025-07-15 09:03:31,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `is_billing_contact` int(1) not null default 0
2025-07-15 09:03:38,464 WARNING database DDL Query made to DB:
create table `tabDaily Checklist Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`checklist_task` varchar(140),
`remarks` text default 'NOT CHECKED',
`issue_photo` text,
`job_card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:38,635 WARNING database DDL Query made to DB:
create table `tabOutsourcing Shift` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`outsourcing_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:38,847 WARNING database DDL Query made to DB:
create table `tabProperty` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`parent_property` varchar(140),
`is_group` int(1) not null default 0,
`photo` text,
`company` varchar(140),
`cost_center` varchar(140),
`unit_owner` varchar(140),
`title_deed_number` varchar(140),
`type` varchar(140),
`bedroom` int(11) not null default 0,
`master_bedroom` int(11) not null default 0,
`common_bathroom` int(11) not null default 0,
`builtup_area` decimal(21,9) not null default 0,
`carpet_area` decimal(21,9) not null default 0,
`facing` varchar(140),
`no_of_parking` int(11) not null default 0,
`rent` decimal(21,9) not null default 0,
`security_deposit` decimal(21,9) not null default 0,
`smoking` int(1) not null default 0,
`furnished` int(1) not null default 0,
`status` varchar(140),
`description` text,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,087 WARNING database DDL Query made to DB:
create table `tabInsurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`insurance_provider` varchar(140),
`insurance_type` varchar(140),
`policy_number` varchar(140),
`premium_price` decimal(21,9) not null default 0,
`effective_date` date,
`expiration_date` date,
`description` varchar(140),
`cover_document` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,263 WARNING database DDL Query made to DB:
create table `tabProperty Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_number` varchar(140),
`name1` varchar(140),
`photo` text,
`unit_owner` varchar(140),
`title_deed_number` varchar(140),
`type` varchar(140),
`bedroom` int(11) not null default 0,
`master_bedroom` int(11) not null default 0,
`common_bathroom` int(11) not null default 0,
`builtup_area` decimal(21,9) not null default 0,
`carpet_area` decimal(21,9) not null default 0,
`facing` varchar(140),
`no_of_parking` int(11) not null default 0,
`rent` decimal(21,9) not null default 0,
`security_deposit` decimal(21,9) not null default 0,
`smoking` int(1) not null default 0,
`furnished` int(1) not null default 0,
`description` text,
`status_active` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,409 WARNING database DDL Query made to DB:
create table `tabSecurity Deposit Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`apartment_no` varchar(140),
`bhk` varchar(140),
`amount` decimal(21,9) not null default 0,
`remarks` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,543 WARNING database DDL Query made to DB:
create table `tabSecurity Attendance Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guard_empid` varchar(140),
`guard_name` varchar(140),
`id_no` varchar(140),
`position` varchar(140),
`status` varchar(140),
`remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,690 WARNING database DDL Query made to DB:
create table `tabLease Invoice Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date_to_invoice` date,
`schedule_start_date` date,
`lease_item` varchar(140),
`paid_by` varchar(140),
`lease_item_name` varchar(140),
`document_type` varchar(140),
`invoice_number` varchar(140),
`sales_order_number` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`currency` varchar(140),
`tax` decimal(21,9) not null default 0,
`invoice_item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,815 WARNING database DDL Query made to DB:
create table `tabSecurity Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_date` date,
`shift_name` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:39,979 WARNING database DDL Query made to DB:
create table `tabKey` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_number` varchar(140) unique,
`status_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,123 WARNING database DDL Query made to DB:
create table `tabTool Item Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`no_of_tool_items` int(11) not null default 0,
`shelf_no` varchar(140),
`location_no` varchar(140),
`status` varchar(140),
`duplicate_of` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `set_name`(`set_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,244 WARNING database DDL Query made to DB:
create table `tabCustom Error Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`seen` int(1) not null default 0,
`method` varchar(140),
`error` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,387 WARNING database DDL Query made to DB:
create table `tabMeter Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_type` varchar(140),
`reading_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,537 WARNING database DDL Query made to DB:
create table `tabChecklist Checkup Area` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`area_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,675 WARNING database DDL Query made to DB:
create table `tabDoor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`door_type` varchar(140) unique,
`lock_type` varchar(140),
`screen_door_attached` int(1) not null default 0,
`inside_color` varchar(140),
`exterior_color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,818 WARNING database DDL Query made to DB:
create table `tabLease Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lease_item` varchar(140),
`frequency` varchar(140),
`amount` decimal(21,9) not null default 0,
`currency_code` varchar(140) default 'USD',
`charge_basis` varchar(140),
`charge_rate` varchar(140),
`witholding_tax` decimal(21,9) not null default 0,
`paid_by` varchar(140),
`invoice_item_group` varchar(140),
`document_type` varchar(140) default 'Sales Invoice',
`valid_from` date,
`is_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:40,937 WARNING database DDL Query made to DB:
create table `tabOutsource Contact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140) unique,
`id_no` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,065 WARNING database DDL Query made to DB:
create table `tabApartment Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`apartment_no` varchar(140),
`bhk` varchar(140),
`rent` decimal(21,9) not null default 0,
`agreement_start_date` date,
`agreement_end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,233 WARNING database DDL Query made to DB:
create table `tabDaily Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`checkup_date` date,
`shift` varchar(140),
`area` varchar(140),
`property` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,376 WARNING database DDL Query made to DB:
create table `tabProperty Amenity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amenity_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,501 WARNING database DDL Query made to DB:
create table `tabChecklist Checkup Area Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,613 WARNING database DDL Query made to DB:
create table `tabMultiSelect Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,749 WARNING database DDL Query made to DB:
create table `tabMeter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140) unique,
`meter_type` varchar(140),
`initial_reading` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,871 WARNING database DDL Query made to DB:
create table `tabUnit Assets` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`maintenable` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:41,992 WARNING database DDL Query made to DB:
create table `tabPaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`color_code` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,121 WARNING database DDL Query made to DB:
create table `tabUnit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,357 WARNING database DDL Query made to DB:
create table `tabLease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`property` varchar(140),
`lease_date` date,
`lease_status` varchar(140),
`signed_agreement_received` int(1) not null default 0,
`stamp_duty_paid_by_tenant` int(1) not null default 0,
`property_owner` varchar(140),
`lease_customer` varchar(140),
`customer` varchar(140),
`property_user` varchar(140),
`start_date` date,
`skip_end_date` int(1) not null default 0,
`end_date` date,
`frequency` varchar(140),
`days_to_invoice_in_advance` int(11) not null default 0,
`notice_period` int(11) not null default 0,
`fit_out_period` int(11) not null default 0,
`security_deposit_currency` varchar(140) default 'USD',
`security_deposit` decimal(21,9) not null default 0,
`security_status` varchar(140),
`security_received_payment` varchar(140),
`security_returned_reference` varchar(140),
`late_payment_interest_percentage` decimal(21,9) not null default 0,
`wtax_paid_by` varchar(140),
`witness_2` varchar(140),
`witness_1` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,539 WARNING database DDL Query made to DB:
create table `tabOutsourcing Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`outsourcing_category` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,673 WARNING database DDL Query made to DB:
create table `tabExit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lease` varchar(140),
`exit_type` varchar(140),
`premature_expiry_date` date,
`exit_details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,808 WARNING database DDL Query made to DB:
create table `tabFlooring` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`flooring_type` varchar(140) unique,
`description` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:42,977 WARNING database DDL Query made to DB:
create table `tabProperty Meter Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140),
`meter_type` varchar(140),
`installation_date` date,
`initial_meter_reading` decimal(21,9) not null default 0,
`invoice_customer` varchar(140),
`status` varchar(140),
`deactivation_date` date,
`property_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,132 WARNING database DDL Query made to DB:
create table `tabTool Item Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_tool_item_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,277 WARNING database DDL Query made to DB:
create table `tabKey Set Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_key_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,408 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`person_name` varchar(140),
`position` varchar(140),
`status` varchar(140),
`remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,543 WARNING database DDL Query made to DB:
create table `tabMeter Reading Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`meter_number` varchar(140),
`previous_meter_reading` decimal(21,9) not null default 0,
`current_meter_reading` decimal(21,9) not null default 0,
`reading_difference` decimal(21,9) not null default 0,
`previous_reading_date` date,
`do_not_create_invoice` int(1) not null default 0,
`invoice_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,656 WARNING database DDL Query made to DB:
create table `tabGuard Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,818 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_date` date,
`shift_name` varchar(140),
`outsourcing_category_name` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:43,961 WARNING database DDL Query made to DB:
create table `tabTool Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_number` varchar(140) unique,
`status_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:44,095 WARNING database DDL Query made to DB:
create table `tabKey Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`no_of_keys` int(11) not null default 0,
`shelf_no` varchar(140),
`location_no` varchar(140),
`status` varchar(140),
`duplicate_of` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `set_name`(`set_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:44,241 WARNING database DDL Query made to DB:
create table `tabIssue Materials Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`material_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:44,359 WARNING database DDL Query made to DB:
create table `tabGuard Shift` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:44,673 WARNING database DDL Query made to DB:
create table `tabIssue Materials Billed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`invoiced` int(1) not null default 0,
`sales_invoice` varchar(140),
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:44,786 WARNING database DDL Query made to DB:
create table `tabOutsourcing Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 09:03:50,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` ADD COLUMN `mateiral_request` varchar(140)
2025-07-15 09:03:50,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-15 09:03:50,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `reading_required` int(1) not null default 0
2025-07-15 09:03:50,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0
2025-07-15 09:03:50,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `security_account_code` varchar(140), ADD COLUMN `default_tax_account_head` varchar(140), ADD COLUMN `default_tax_template` varchar(140), ADD COLUMN `default_maintenance_tax_template` varchar(140)
2025-07-15 09:03:50,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-07-15 09:03:50,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `cost_center` varchar(140)
2025-07-15 09:03:50,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-07-15 09:03:50,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `material_request` varchar(140)
2025-07-15 09:03:50,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-07-15 09:03:51,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `lease` varchar(140), ADD COLUMN `lease_item` varchar(140), ADD COLUMN `job_card` varchar(140)
2025-07-15 09:03:51,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0
2025-07-15 09:03:51,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `sales_invoice` varchar(140)
2025-07-15 09:03:51,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-07-15 09:03:51,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `property_name` varchar(140), ADD COLUMN `person_in_charge` varchar(140), ADD COLUMN `sub_contractor_contact` varchar(140), ADD COLUMN `person_in_charge_name` varchar(140), ADD COLUMN `sub_contractor_name` varchar(140), ADD COLUMN `defect_found` longtext, ADD COLUMN `customer_feedback` longtext
2025-07-15 09:03:51,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
2025-07-15 09:03:51,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` ADD COLUMN `territory` varchar(140)
2025-07-15 09:03:51,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` MODIFY `carpet_area` decimal(21,9) not null default 0, MODIFY `security_deposit` decimal(21,9) not null default 0, MODIFY `rent` decimal(21,9) not null default 0, MODIFY `builtup_area` decimal(21,9) not null default 0
2025-07-15 09:06:25,077 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-15 09:06:26,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-15 09:06:28,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-15 13:57:51,043 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-15 13:57:53,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-15 13:57:55,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-15 13:58:13,389 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-15 13:58:15,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-15 13:58:18,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-15 13:58:21,089 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-15 13:58:21,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-15 13:58:24,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-15 13:58:24,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-15 13:58:35,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-07-15 13:59:42,010 WARNING database DDL Query made to DB:
create table `tabCV Pack Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`include_in_export` int(1) not null default 1,
`custom_template` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:42,263 WARNING database DDL Query made to DB:
create table `tabCV Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140) unique,
`proficiency_level` varchar(140),
`years_of_experience` decimal(21,9) not null default 0,
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:42,504 WARNING database DDL Query made to DB:
create table `tabCV Language` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`language` varchar(140),
`proficiency` varchar(140),
`reading` varchar(140),
`writing` varchar(140),
`speaking` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:42,836 WARNING database DDL Query made to DB:
create table `tabEmployee CV Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140) unique,
`employee_name` varchar(140),
`cv_title` varchar(140),
`cv_visibility` varchar(140) default 'Internal',
`summary` text,
`public_token` varchar(140),
`token_valid_until` datetime(6),
`last_exported` datetime(6),
`export_count` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:43,097 WARNING database DDL Query made to DB:
create table `tabCV Project` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`project_title` varchar(140),
`client` varchar(140),
`role` varchar(140),
`duration_from` date,
`duration_to` date,
`description` text,
`technologies` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:43,366 WARNING database DDL Query made to DB:
create table `tabCV Pack` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`pack_name` varchar(140) unique,
`description` text,
`status` varchar(140) default 'Draft',
`created_by` varchar(140) default 'user',
`export_format` varchar(140) default 'PDF',
`template` varchar(140),
`last_exported` datetime(6),
`export_count` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:43,621 WARNING database DDL Query made to DB:
create table `tabCV Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`is_default` int(1) not null default 0,
`template_type` varchar(140) default 'Professional',
`status` varchar(140) default 'Active',
`description` text,
`template_html` longtext,
`preview_image` text,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:43,873 WARNING database DDL Query made to DB:
create table `tabCV Certification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`certification_name` varchar(140),
`issuing_organization` varchar(140),
`date_awarded` date,
`expiry_date` date,
`credential_id` varchar(140),
`credential_url` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 13:59:46,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `cv_profile_link` varchar(140)
2025-07-15 13:59:46,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-15 17:12:33,829 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-15 17:12:35,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-15 17:12:37,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-15 17:12:38,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-15 17:12:39,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-15 17:12:41,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-15 17:12:41,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-15 17:12:50,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
