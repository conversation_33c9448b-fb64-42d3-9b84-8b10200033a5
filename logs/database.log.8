2025-07-14 16:29:48,298 WARNING database DDL Query made to DB:
create table `tabElectronics Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronic_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:48,449 WARNING database DDL Query made to DB:
create table `tabBrake System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:48,593 WARNING database DDL Query made to DB:
create table `tabEquipment Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`equipment` varchar(140),
`quantity` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:48,739 WARNING database DDL Query made to DB:
create table `tabAir System Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:48,881 WARNING database DDL Query made to DB:
create table `tabPower Train Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`power_train_checklist` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,028 WARNING database DDL Query made to DB:
create table `tabElectrical Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electrical_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,186 WARNING database DDL Query made to DB:
create table `tabSuspension Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,362 WARNING database DDL Query made to DB:
create table `tabSteering Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`steering_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,507 WARNING database DDL Query made to DB:
create table `tabExpense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`fixed_value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,660 WARNING database DDL Query made to DB:
create table `tabFixed Expense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`currency` varchar(140),
`fixed_value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,810 WARNING database DDL Query made to DB:
create table `tabPower Train Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`power_train_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:49,988 WARNING database DDL Query made to DB:
create table `tabAssigned Transport Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cargo` varchar(140),
`container_number` varchar(140),
`assigned_vehicle` varchar(140),
`assigned_trailer` varchar(140),
`assigned_driver` varchar(140),
`driver_name` varchar(140),
`passport_number` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`loading_date` date,
`lodge_permit` date,
`dispatch_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:50,170 WARNING database DDL Query made to DB:
create table `tabFuel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_plate_number` varchar(140),
`company` varchar(140),
`main_route` varchar(140),
`main_approved_fuel` varchar(140),
`return_route` varchar(140),
`return_approved_fuel` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`status` varchar(140) default 'Waiting Approval',
`transaction_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:50,602 WARNING database DDL Query made to DB:
create table `tabVehicle Trip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`transporter_type` varchar(140),
`sub_contractor` varchar(140),
`vehicle` varchar(140),
`vehicle_plate_number` varchar(140),
`trailer` varchar(140),
`trailer_plate_number` varchar(140),
`driver` varchar(140),
`driver_name` varchar(140),
`passport_number` varchar(140),
`driving_licence_no` varchar(140),
`phone_number` varchar(140),
`start_date` date,
`main_customer` varchar(140),
`main_cargo_type` varchar(140),
`main_cargo_category` varchar(140),
`main_goods_description` varchar(140),
`main_amount` decimal(21,9) not null default 0,
`main_unit` varchar(140),
`main_loose_no_of_packages` int(11) not null default 0,
`main_loose_gross_weight` int(11) not null default 0,
`main_loose_net_weight` int(11) not null default 0,
`main_shipper` varchar(140),
`main_consignee` varchar(140),
`main_cargo_location_country` varchar(140),
`main_cargo_location_city` varchar(140),
`main_loading_point` varchar(140),
`main_cargo_destination_country` varchar(140),
`main_cargo_destination_city` varchar(140),
`main_offloading_point` varchar(140),
`main_eta` datetime(6),
`main_route` varchar(140),
`main_shipment_ref_no` varchar(140),
`main_client_ref_no` varchar(140),
`main_border1_clearing` varchar(140),
`main_border2_clearing` varchar(140),
`main_border3_clearing` varchar(140),
`main_special_instructions_transporter` varchar(140),
`main_special_instructions_to_driver` varchar(140),
`main_delivery_note` text,
`main_offloading_report` text,
`main_offloading_weight` date,
`main_approved_fuel` varchar(140),
`return_start_date` date,
`return_customer` varchar(140),
`return_cargo_type` varchar(140),
`return_cargo_category` varchar(140),
`return_goods_description` varchar(140),
`return_amount` decimal(21,9) not null default 0,
`return_unit` varchar(140),
`return_loose_no_of_packages` int(11) not null default 0,
`return_loose_gross_weight` int(11) not null default 0,
`return_loose_net_weight` int(11) not null default 0,
`return_shipper` varchar(140),
`return_consignee` varchar(140),
`return_cargo_location_country` varchar(140),
`return_cargo_location_city` varchar(140),
`return_loading_point` varchar(140),
`return_cargo_destination_country` varchar(140),
`return_cargo_destination_city` varchar(140),
`return_offloading_point` varchar(140),
`return_eta` date,
`return_route` varchar(140),
`return_shipment_ref_no` varchar(140),
`return_client_ref_no` varchar(140),
`return_border1_clearing` varchar(140),
`return_border2_clearing` varchar(140),
`return_border3_clearing` varchar(140),
`return_special_instructions_transporter` varchar(140),
`return_special_instructions_driver` varchar(140),
`return_delivery_note` text,
`return_offloading_report` text,
`return_offloading_weight` date,
`return_approved_fuel` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`main_file_number` varchar(140),
`main_import` varchar(140),
`main_export` varchar(140),
`return_reference_doctype` varchar(140),
`return_reference_docname` varchar(140),
`return_file_number` varchar(140),
`return_import` varchar(140),
`return_export` varchar(140),
`main_status` varchar(140),
`return_status` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Open',
`hidden_status` int(11) not null default 0,
`main_address_display` longtext,
`main_consignee_display` longtext,
`main_shipper_display` longtext,
`return_consignee_display` longtext,
`return_shipper_display` longtext,
`return_address_display` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:50,759 WARNING database DDL Query made to DB:
create table `tabFuel System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fuel_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:50,913 WARNING database DDL Query made to DB:
create table `tabTrip Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`location` varchar(140),
`is_local_border` int(1) not null default 0,
`is_international_border` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:51,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `return_date` date, ADD COLUMN `return_reason` varchar(140), ADD COLUMN `odometer_on_return` int(11) not null default 0, ADD COLUMN `return_condition` text
2025-07-14 16:29:51,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-14 16:29:51,416 WARNING database DDL Query made to DB:
create table `tabFuel Request Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`quantity` decimal(21,9) not null default 0,
`disburcement_type` varchar(140),
`supplier` varchar(140),
`cost_per_litre` decimal(21,9) not null default 0,
`total_cost` decimal(21,9) not null default 0,
`status` varchar(140) default 'Open',
`uom` varchar(140),
`approved_by` varchar(140),
`approved_date` date,
`receipt_date` date,
`receipt_time` time(6),
`received_by` varchar(140),
`transaction_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:51,575 WARNING database DDL Query made to DB:
create table `tabTires Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:51,713 WARNING database DDL Query made to DB:
create table `tabLighting Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lighting_check_item` varchar(140),
`lighting_mark` int(1) not null default 0,
`lighting_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:51,863 WARNING database DDL Query made to DB:
create table `tabSubtrips Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_location` varchar(140),
`destination_location` varchar(140),
`approximate_distance` int(11) not null default 0,
`source_departure` date,
`destination_arrival` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,007 WARNING database DDL Query made to DB:
create table `tabSteering Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`steering_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,160 WARNING database DDL Query made to DB:
create table `tabVehicle Trip Location Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`location` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`comment` varchar(140),
`type_of_update` varchar(140) default 'Manual Update',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,432 WARNING database DDL Query made to DB:
create table `tabVehicle Documents` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`reference_no` varchar(140),
`issue_date` date,
`expire_date` date,
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,583 WARNING database DDL Query made to DB:
create table `tabTire Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tire_position` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,766 WARNING database DDL Query made to DB:
create table `tabRoute Steps Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
`location_type` varchar(140),
`arrival_date` date,
`departure_date` date,
`loading_date` date,
`offloading_date` date,
`documents_from_driver` datetime(6),
`doc_submitted_to_agent` date,
`doc_received_by` varchar(140),
`crossing_time` datetime(6),
`comment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:52,907 WARNING database DDL Query made to DB:
create table `tabFuel System Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fuel_system` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,060 WARNING database DDL Query made to DB:
create table `tabVehicle Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lighting_check_item` varchar(140),
`lighting_mark` int(1) not null default 0,
`brake_check_item` varchar(140),
`brake_mark` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,203 WARNING database DDL Query made to DB:
create table `tabElectronics Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronics_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,369 WARNING database DDL Query made to DB:
create table `tabTrailer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_plate` varchar(140) unique,
`chassis_number` varchar(140),
`make` varchar(140),
`year` int(11) not null default 0,
`axles` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,511 WARNING database DDL Query made to DB:
create table `tabVehicle Routine Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`data_1` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,688 WARNING database DDL Query made to DB:
create table `tabTrip Route` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`route_name` varchar(140),
`total_distance` int(11) not null default 0,
`total_tzs` decimal(21,9) not null default 0,
`total_usd` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:53,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-14 16:29:54,024 WARNING database DDL Query made to DB:
create table `tabAir System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parts` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:54,224 WARNING database DDL Query made to DB:
create table `tabVehicle Inspection Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:54,373 WARNING database DDL Query made to DB:
create table `tabLighting Checklist Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:54,566 WARNING database DDL Query made to DB:
create table `tabVehicle Documents Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:54,825 WARNING database DDL Query made to DB:
create table `tabTransport Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_received` date,
`customer` varchar(140),
`loading_date` date,
`transport_type` varchar(140),
`cargo_location_country` varchar(140),
`cargo_location_city` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`consignee` varchar(140),
`shipper` varchar(140),
`border1_clearing_agent` varchar(140),
`border2_clearing_agent` varchar(140),
`border3_clearing_agent` varchar(140),
`special_instructions_to_transporter` text,
`cargo_type` varchar(140),
`goods_description` varchar(140),
`cargo_description` varchar(140),
`amount` decimal(21,9) not null default 0,
`unit` varchar(140),
`total_assigned` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`cargo_name` varchar(140),
`file_number` varchar(140),
`assignment_status` varchar(140) default 'Waiting Assignment',
`version` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:54,968 WARNING database DDL Query made to DB:
create table `tabElectrical Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electrical_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,119 WARNING database DDL Query made to DB:
create table `tabSuspension Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parts` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,258 WARNING database DDL Query made to DB:
create table `tabEngine Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,410 WARNING database DDL Query made to DB:
create table `tabTires Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,553 WARNING database DDL Query made to DB:
create table `tabEquipment Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,711 WARNING database DDL Query made to DB:
create table `tabEngine Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`engine_system` varchar(140),
`mark` int(1) not null default 0,
`engine_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:55,866 WARNING database DDL Query made to DB:
create table `tabUnit of Measure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:56,034 WARNING database DDL Query made to DB:
create table `tabTrip Steps Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
`distance` decimal(21,9) not null default 0,
`location_type` varchar(140),
`is_local_border` int(1) not null default 0,
`is_international_border` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:56,187 WARNING database DDL Query made to DB:
create table `tabVehicle Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:56,355 WARNING database DDL Query made to DB:
create table `tabTrip Location Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_type` varchar(140),
`arrival_date` int(1) not null default 0,
`departure_date` int(1) not null default 0,
`loading_date` int(1) not null default 0,
`offloading_date` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,053 WARNING database DDL Query made to DB:
create table `tabPurchase And Stock Management Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`pstest` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,204 WARNING database DDL Query made to DB:
create table `tabOrder Tracking Container` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`co_name` varchar(140),
`co_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,397 WARNING database DDL Query made to DB:
create table `tabOrder Track` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`supplier_type` varchar(140),
`expected_arrival_date` date,
`arrival_date` date,
`shipped_date` date,
`bl_number` varchar(140),
`discharged_date` date,
`mode_of_transport` varchar(140),
`clearing_company` varchar(140),
`bl_received` datetime(6),
`clearing_agent_bl` datetime(6),
`expected_clearing_completion_date` date,
`clearing_completion_date` date,
`date_of_invoice` date,
`date_received_registration_card` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,559 WARNING database DDL Query made to DB:
create table `tabBin List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`current_label` varchar(140),
`new_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,810 WARNING database DDL Query made to DB:
create table `tabPast Sales` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`item_sold` varchar(140),
`customer` varchar(140),
`amount` decimal(21,9) not null default 0,
`sold_date` date,
`serial_no` varchar(140),
`plate_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:57,982 WARNING database DDL Query made to DB:
create table `tabAllert Custom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:58,141 WARNING database DDL Query made to DB:
create table `tabPayment Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`planned_date` date,
`planned_amount` decimal(21,9) not null default 0,
`actual_date` date,
`actual_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:58,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` ADD COLUMN `item_code` varchar(140), ADD COLUMN `serial_no` varchar(140), ADD COLUMN `reference` varchar(140)
2025-07-14 16:29:58,457 WARNING database DDL Query made to DB:
create table `tabMarketing Dept` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dept_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:58,640 WARNING database DDL Query made to DB:
create table `tabPast Serial No` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`item_code` varchar(140),
`past_item_code` varchar(140),
`customer` varchar(140),
`past_item_group` varchar(140),
`amount` decimal(21,9) not null default 0,
`sales_ref_no` varchar(140),
`date_of_sale` date,
`plate_number` varchar(140),
`extra_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:58,900 WARNING database DDL Query made to DB:
create table `tabProducts of Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`extra_details` varchar(140),
`customer_item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`pricing_rule` varchar(140),
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`projected_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`item_tax_rate` longtext,
`page_break` int(1) not null default 0,
`item_group` varchar(140),
`brand` varchar(140),
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:59,056 WARNING database DDL Query made to DB:
create table `tabCommunications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type_of_communication` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`contacted_by` varchar(140),
`date_of_communication` datetime(6),
`communication_feedback` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:59,325 WARNING database DDL Query made to DB:
create table `tabWorkshop Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:59,516 WARNING database DDL Query made to DB:
create table `tabIssued Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`requested` decimal(21,9) not null default 0,
`issued` decimal(21,9) not null default 0,
`difference` decimal(21,9) not null default 0,
`units` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:59,653 WARNING database DDL Query made to DB:
create table `tabWorkshop Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:29:59,837 WARNING database DDL Query made to DB:
create table `tabRequested Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`description` longtext,
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`requested_for` varchar(140),
`status` varchar(140) default 'Open',
`recommended_by` varchar(140),
`recommended_date` datetime(6),
`approved_by` varchar(140),
`approved_date` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:00,004 WARNING database DDL Query made to DB:
create table `tabRequested Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`approval_status` varchar(140) default 'Waiting Approval',
`items_issue_status` varchar(140) default 'Waiting Approval',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:00,164 WARNING database DDL Query made to DB:
create table `tabWorkshop Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`requested_for` varchar(140),
`requested_for_docname` varchar(140),
`requested_date` date,
`request_type` varchar(140),
`previous_job` varchar(140),
`details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:00,340 WARNING database DDL Query made to DB:
create table `tabWorkshop Services Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`work_done` text,
`service_status` varchar(140),
`subcontracted` int(1) not null default 0,
`subcontractor` varchar(140),
`technician` varchar(140),
`start_date` date,
`start_time` time(6),
`end_date` date,
`end_time` time(6),
`billable_hours` decimal(21,9) not null default 0,
`rate_per_hour` decimal(21,9) not null default 0,
`currency_rate_per_hour` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:00,494 WARNING database DDL Query made to DB:
create table `tabUsed Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`item_description` varchar(140),
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`extra_information` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:00,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabOTP Register` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `otp_secret` text, ADD COLUMN `registered` int(1) not null default 0, ADD COLUMN `amended_from` varchar(140)
2025-07-14 16:30:01,097 WARNING database DDL Query made to DB:
create table `tabCSF TZ Meal Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meal_name` varchar(140) unique,
`meal_type` varchar(140),
`start_time` time(6),
`end_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:01,269 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`device_id` varchar(140) unique,
`device_nick_name` varchar(140),
`installed_date` date,
`device_site_name` varchar(140),
`device_location` varchar(140),
`device_supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:01,459 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`punch` varchar(140),
`user_id` varchar(140),
`uid` varchar(140),
`status` varchar(140),
`timestamp` datetime(6),
`device_id` varchar(140),
`device_ip` varchar(140),
`punch_direction` varchar(140),
`biometric_user` varchar(140),
`biometric_user_name` varchar(140),
`biometric` varchar(140),
`meal_type` varchar(140),
`site_name` varchar(140),
`supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:01,621 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:01,794 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140) unique,
`uid` varchar(140),
`erpnext_user` varchar(140),
`user_name` varchar(140),
`user_type` varchar(140),
`amended_from` varchar(140),
`gender` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:02,041 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Initiation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`file_code` varchar(140),
`amended_from` varchar(140),
`posting_date` date,
`posting_time` time(6),
`number_of_transactions` int(11) not null default 0,
`control_sum` decimal(21,9) not null default 0,
`stanbic_setting` varchar(140),
`initiating_party_name` varchar(140),
`customer_id` varchar(140),
`ordering_customer_name` varchar(140),
`ordering_customer_account_country` varchar(140) default 'TZ',
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140) default 'DEBT',
`xml` longtext,
`encrypted_xml` longtext,
`stanbic_ack` longtext,
`stanbic_intaud` longtext,
`stanbic_finaud` longtext,
`stanbic_ack_status` varchar(140),
`stanbic_ack_change` int(1) not null default 0,
`stanbic_intaud_change` int(1) not null default 0,
`stanbic_finaud_change` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:02,305 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Info` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_slip` varchar(140),
`employee` varchar(140),
`transfer_currency` varchar(140),
`transfer_amount` decimal(21,9) not null default 0,
`beneficiary_bank_bic` varchar(140),
`beneficiary_bank_sort_code` varchar(140),
`beneficiary_bank_name` varchar(140),
`beneficiary_bank_country_code` varchar(140),
`beneficiary_name` varchar(140),
`beneficiary_country` varchar(140),
`beneficiary_address` varchar(140),
`beneficiary_iban` varchar(140),
`beneficiary_account_number` varchar(140),
`beneficiary_account_type` int(11) not null default 0,
`beneficiary_account_currency` varchar(140),
`stanbic_finaud_status` varchar(140),
`stanbic_intaud_status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:02,508 WARNING database DDL Query made to DB:
create table `tabStanbic Setting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`enabled` int(1) not null default 1,
`currency` varchar(140),
`sftp_url` varchar(140),
`sftp_user` varchar(140),
`private_key` text,
`port` int(11) not null default 0,
`pgp_public_key` text,
`pgp_private_key` text,
`initiating_party_name` varchar(140),
`customerid` varchar(140),
`payment_type` varchar(140),
`user` varchar(140),
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140),
`file_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:19,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `weight_per_unit` decimal(21,9) not null default 0, ADD COLUMN `total_weight` decimal(21,9) not null default 0, ADD COLUMN `weight_uom` varchar(140)
2025-07-14 16:30:19,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0
2025-07-14 16:30:19,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `is_ignored_in_pending_qty` int(1) not null default 0, ADD COLUMN `allow_over_sell` int(1) not null default 0, ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140), ADD COLUMN `allow_override_net_rate` int(1) not null default 0, ADD COLUMN `delivery_status` varchar(140)
2025-07-14 16:30:19,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-07-14 16:30:19,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `creation`(`creation`)
2025-07-14 16:30:19,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `material_request` varchar(140)
2025-07-14 16:30:19,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-14 16:30:20,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` ADD COLUMN `image` text
2025-07-14 16:30:20,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-07-14 16:30:20,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `vrn` varchar(140)
2025-07-14 16:30:20,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-14 16:30:20,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `previous_invoice_number` varchar(140), ADD COLUMN `default_item_discount` decimal(21,9) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `price_reduction` decimal(21,9) not null default 0, ADD COLUMN `tra_control_number` varchar(140), ADD COLUMN `witholding_tax_certificate_number` varchar(140), ADD COLUMN `electronic_fiscal_device` varchar(140), ADD COLUMN `efd_z_report` varchar(140), ADD COLUMN `excise_duty_applicable` int(1) not null default 0, ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1, ADD COLUMN `delivery_status` varchar(140)
2025-07-14 16:30:20,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-07-14 16:30:20,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` ADD COLUMN `fully_paid` int(1) not null default 0
2025-07-14 16:30:20,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-07-14 16:30:20,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140)
2025-07-14 16:30:20,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-07-14 16:30:20,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-07-14 16:30:20,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-07-14 16:30:20,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-07-14 16:30:20,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-07-14 16:30:20,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `posting_date` date, ADD COLUMN `start_date` date, ADD COLUMN `end_date` date
2025-07-14 16:30:20,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-07-14 16:30:20,873 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `stock_reconciliation` varchar(140)
2025-07-14 16:30:20,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-07-14 16:30:20,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-07-14 16:30:20,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-07-14 16:30:21,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `vrn` varchar(140)
2025-07-14 16:30:21,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `posting_date` date, ADD COLUMN `default_item_discount` decimal(21,9) not null default 0
2025-07-14 16:30:21,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-07-14 16:30:21,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140)
2025-07-14 16:30:21,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-07-14 16:30:21,468 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `form_sales_invoice` varchar(140)
2025-07-14 16:30:21,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-07-14 16:30:21,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `trip_destination` varchar(140), ADD COLUMN `destination_description` varchar(140)
2025-07-14 16:30:21,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-14 16:30:21,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `old_employee_id` varchar(140), ADD COLUMN `heslb_f4_index_number` varchar(140)
2025-07-14 16:30:21,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-14 16:30:21,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD COLUMN `spare_name` varchar(140), ADD COLUMN `quantity` decimal(21,9) not null default 0, ADD COLUMN `invoice` varchar(140)
2025-07-14 16:30:21,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-14 16:30:21,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `posting_date` date
2025-07-14 16:30:21,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0
2025-07-14 16:30:21,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom DocPerm` ADD COLUMN `dependent` int(1) not null default 0
2025-07-14 16:30:22,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_bank_details` text, ADD COLUMN `vrn` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `p_o_box` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `plot_number` varchar(140), ADD COLUMN `block_number` varchar(140), ADD COLUMN `street` varchar(140), ADD COLUMN `max_records_in_dialog` int(11) not null default 0, ADD COLUMN `default_withholding_payable_account` varchar(140), ADD COLUMN `auto_create_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `default_withholding_receivable_account` varchar(140), ADD COLUMN `auto_create_for_sales_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_sales_withholding` int(1) not null default 0, ADD COLUMN `bypass_material_request_validation` int(1) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1
2025-07-14 16:30:22,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-14 16:30:22,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `csf_tz_year` int(11) not null default 0, ADD COLUMN `csf_tz_acquisition_odometer` int(11) not null default 0, ADD COLUMN `csf_tz_engine_number` varchar(140)
2025-07-14 16:30:22,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `uom` varchar(140), MODIFY `vehicle_value` decimal(21,9) not null default 0
2025-07-14 16:30:22,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140), ADD COLUMN `referance_doctype` varchar(140), ADD COLUMN `referance_docname` varchar(140), ADD COLUMN `from_date` date, ADD COLUMN `to_date` date
2025-07-14 16:30:22,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0
2025-07-14 16:30:22,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, ADD COLUMN `excisable_item` int(1) not null default 0, ADD COLUMN `default_tax_template` varchar(140)
2025-07-14 16:30:22,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0
2025-07-14 16:30:22,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `electronic_fiscal_device` varchar(140)
2025-07-14 16:30:22,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-07-14 16:30:22,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `repack_template` varchar(140), ADD COLUMN `item_uom` varchar(140), ADD COLUMN `repack_qty` decimal(21,9) not null default 0, ADD COLUMN `final_destination` varchar(140), ADD COLUMN `total_net_weight` decimal(21,9) not null default 0, ADD COLUMN `transporter` varchar(140), ADD COLUMN `driver` varchar(140), ADD COLUMN `transport_receipt_no` varchar(140), ADD COLUMN `vehicle_no` varchar(140), ADD COLUMN `transporter_name` varchar(140), ADD COLUMN `driver_name` varchar(140), ADD COLUMN `transport_receipt_date` date
2025-07-14 16:30:22,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0
2025-07-14 16:30:22,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) not null default 0
2025-07-14 16:30:23,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-07-14 16:30:23,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0
2025-07-14 16:30:23,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-07-14 16:30:23,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-07-14 16:30:24,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-07-14 16:30:24,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-14 16:30:25,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-07-14 16:30:25,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-07-14 16:30:26,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-07-14 16:30:26,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0
2025-07-14 16:30:26,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-07-14 16:30:26,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-14 16:30:27,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-07-14 16:30:27,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0
2025-07-14 16:30:27,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-07-14 16:30:27,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-07-14 16:30:27,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-07-14 16:30:27,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-07-14 16:30:27,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-14 16:30:28,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-07-14 16:30:28,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-14 16:30:28,490 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-07-14 16:30:28,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-07-14 16:30:28,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-07-14 16:30:28,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-07-14 16:30:28,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0
2025-07-14 16:30:29,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-07-14 16:30:29,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-07-14 16:30:29,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employee_country` varchar(140), ADD COLUMN `employee_country_code` varchar(140), ADD COLUMN `beneficiary_bank_bic` varchar(140), ADD COLUMN `bank_country_code` varchar(140), ADD COLUMN `bank_country` varchar(140), ADD COLUMN `bank_account_name` varchar(140)
2025-07-14 16:30:29,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-14 16:30:30,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0
2025-07-14 16:30:30,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-07-14 16:30:30,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-07-14 16:30:30,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-07-14 16:30:30,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
