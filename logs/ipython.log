2025-07-14 09:38:33,389 INFO ipython === bench console session ===
2025-07-14 09:38:33,389 INFO ipython import frappe
2025-07-14 09:38:33,389 INFO ipython frappe.get_all("DocType", filters={"module": "CV Manager"})
2025-07-14 09:38:33,390 INFO ipython from cv_maker.fixtures.test_data import create_test_data
2025-07-14 09:38:33,390 INFO ipython result = create_test_data()
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Company", limit=1)
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Employee", limit=1)
2025-07-14 09:38:33,390 INFO ipython template = frappe.new_doc("CV Template")
2025-07-14 09:38:33,391 INFO ipython template.template_name = "Test Template"
2025-07-14 09:38:33,391 INFO ipython template.template_html = "<h1>{{ employee_name }}</h1>"
2025-07-14 09:38:33,391 INFO ipython template.status = "Active"
2025-07-14 09:38:33,391 INFO ipython # First, let's create a Company
2025-07-14 09:38:33,391 INFO ipython company = frappe.new_doc("Company")
2025-07-14 09:38:33,392 INFO ipython company.company_name = "Mysite Co."
2025-07-14 09:38:33,392 INFO ipython company.abbr = "MSC"
2025-07-14 09:38:33,392 INFO ipython company.default_currency = "USD"
2025-07-14 09:38:33,392 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython company.country = "United States"
2025-07-14 09:38:33,393 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython frappe.get_all("Company")
2025-07-14 09:38:33,393 INFO ipython frappe.db.sql("SELECT name FROM `tabCompany`")
2025-07-14 09:38:33,393 INFO ipython frappe.db.commit()
2025-07-14 09:38:33,394 INFO ipython company = frappe.get_doc("Company", "Mysite Co.")
2025-07-14 09:38:33,394 INFO ipython # Create departments
2025-07-14 09:38:33,394 INFO ipython departments = ["Technology", "Product", "Design", "Analytics", "Human Resources"]
2025-07-14 09:38:33,394 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,394 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,395 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,395 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,395 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,396 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,396 INFO ipython === session end ===
2025-07-14 09:49:47,231 INFO ipython === bench console session ===
2025-07-14 09:49:47,231 INFO ipython import frappe
2025-07-14 09:49:47,232 INFO ipython # Clear existing CV profiles and child table data
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabEmployee CV Profile`")
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabCV Skill`")
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabCV Language`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.sql("DELETE FROM `tabCV Project`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.sql("DELETE FROM `tabCV Certification`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.commit()
2025-07-14 09:49:47,233 INFO ipython === session end ===
2025-07-14 16:15:16,347 INFO ipython === bench console session ===
2025-07-14 16:15:16,356 INFO ipython import frappe
2025-07-14 16:15:16,357 INFO ipython # Test the export functionality
2025-07-14 16:15:16,357 INFO ipython from cv_maker.api import export_cv_profile
2025-07-14 16:15:16,357 INFO ipython # Get the first CV profile
2025-07-14 16:15:16,358 INFO ipython cv_profiles = frappe.get_all("Employee CV Profile", limit=1)
2025-07-14 16:15:16,358 INFO ipython print(cv_profiles)
2025-07-14 16:15:16,358 INFO ipython # Test PDF export
2025-07-14 16:15:16,358 INFO ipython result = export_cv_profile("CV-HR-EMP-00003", "PDF")
2025-07-14 16:15:16,359 INFO ipython print("PDF Export Result:", result)
2025-07-14 16:15:16,359 INFO ipython # Test DOCX export
2025-07-14 16:15:16,359 INFO ipython docx_result = export_cv_profile("CV-HR-EMP-00003", "DOCX")
2025-07-14 16:15:16,359 INFO ipython print("DOCX Export successful! Length:", len(docx_result))
2025-07-14 16:15:16,360 INFO ipython # Test HTML export
2025-07-14 16:15:16,360 INFO ipython html_result = export_cv_profile("CV-HR-EMP-00003", "HTML")
2025-07-14 16:15:16,360 INFO ipython print("HTML Export successful! Length:", len(html_result))
2025-07-14 16:15:16,360 INFO ipython # Test public link generation
2025-07-14 16:15:16,361 INFO ipython from cv_maker.api import generate_public_link
2025-07-14 16:15:16,361 INFO ipython public_link = generate_public_link("CV-HR-EMP-00003", 30)
2025-07-14 16:15:16,361 INFO ipython print("Public link generated:", public_link)
2025-07-14 16:15:16,361 INFO ipython === session end ===
2025-07-15 14:04:53,398 INFO ipython === bench console session ===
2025-07-15 14:04:53,414 INFO ipython import frappe
2025-07-15 14:04:53,414 INFO ipython frappe.get_all("CV Template", fields=["name", "template_name", "is_default"])
2025-07-15 14:04:53,414 INFO ipython === session end ===
2025-07-15 17:09:56,623 INFO ipython === bench console session ===
2025-07-15 17:09:56,640 INFO ipython import frappe
2025-07-15 17:09:56,640 INFO ipython import json
2025-07-15 17:09:56,640 INFO ipython # Read the fixture file
2025-07-15 17:09:56,641 INFO ipython with open('apps/cv_maker/cv_maker/fixtures/cv_template.json', 'r') as f:
        templates = json.load(f)
        
2025-07-15 17:09:56,641 INFO ipython # Update each template in the database
2025-07-15 17:09:56,644 INFO ipython for template_data in templates:
        template_name = template_data['name']
            new_html = template_data['template_html']
2025-07-15 17:09:56,644 INFO ipython     # Get the existing template
2025-07-15 17:09:56,648 INFO ipython     template = frappe.get_doc('CV Template', template_name)
2025-07-15 17:09:56,649 INFO ipython     template.template_html = new_html
2025-07-15 17:09:56,649 INFO ipython     template.save()
2025-07-15 17:09:56,651 INFO ipython     print(f"Updated template: {template_name}")
2025-07-15 17:09:56,652 INFO ipython frappe.db.commit()
2025-07-15 17:09:56,652 INFO ipython print("All templates updated successfully!")
2025-07-15 17:09:56,652 INFO ipython === session end ===
2025-07-15 17:14:26,227 INFO ipython === bench console session ===
2025-07-15 17:14:26,228 INFO ipython import frappe
2025-07-15 17:14:26,228 INFO ipython template = frappe.get_doc("CV Template", "Minimalist CV")
2025-07-15 17:14:26,233 INFO ipython print("Template HTML length:", len(template.template_html))
2025-07-15 17:14:26,233 INFO ipython print("Template contains employee_name:", "{{ employee_name }}" in template.template_html)
2025-07-15 17:14:26,233 INFO ipython print("Template contains cv_title:", "{{ cv_title }}" in template.template_html)
2025-07-15 17:14:26,233 INFO ipython print("First 200 chars:", template.template_html[:200])
2025-07-15 17:14:26,234 INFO ipython === session end ===
